
import React, { useState } from 'react';
import { useDocumentConvert } from '@/hooks/useDocumentConvert';
import { useDocumentHistory } from '@/hooks/useDocumentHistory';
import Header from '@/components/layout/Header';
import { DocumentPageTitle } from '@/components/document/DocumentPageTitle';
import { DocumentHistoryPanel } from '@/components/document/DocumentHistoryPanel';
import { DocumentUploadStep } from '@/components/document/DocumentUploadStep';
import { DocumentSummaryStep } from '@/components/document/DocumentSummaryStep';
import { DocumentResultStep } from '@/components/document/DocumentResultStep';
import { Button } from '@/components/ui/button';
import { History } from 'lucide-react';

const DocumentConvert = () => {
  const [showHistory, setShowHistory] = useState(false);
  
  const {
    file,
    isProcessing,
    isExtractingSummary,
    isGeneratingPopularized,
    originalContent,
    popularizedContent,
    documentSummary,
    selectedSections,
    currentStep,
    handleFileChange,
    handleRemoveFile,
    handleExtractSummary,
    handleGeneratePopularized,
    setSelectedSections,
    handleRestart
  } = useDocumentConvert();

  const {
    historyItems,
    isLoading: isLoadingHistory,
    fetchHistory,
    deleteHistoryItem,
    copyContent
  } = useDocumentHistory();

  const handleToggleHistory = () => {
    setShowHistory(!showHistory);
    if (!showHistory && historyItems.length === 0 && !isLoadingHistory) {
      fetchHistory();
    }
  };

  const handleCloseHistory = () => {
    setShowHistory(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* 使用统一的Header */}
      <Header />

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="w-full">
          {/* 页面标题和历史记录按钮 */}
          <DocumentPageTitle>
            <Button 
              variant="ghost" 
              onClick={handleToggleHistory} 
              className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 hover:text-blue-800 rounded-xl px-4 py-2 transition-all duration-300 hover:shadow-lg hover:shadow-blue-200/50"
            >
              <History className="w-4 h-4" />
              <span>历史记录</span>
            </Button>
          </DocumentPageTitle>

          {/* 根据当前步骤渲染不同内容 */}
          {currentStep === 'upload' && (
            <DocumentUploadStep
              file={file}
              isProcessing={isProcessing}
              isExtractingSummary={isExtractingSummary}
              onFileChange={handleFileChange}
              onRemoveFile={handleRemoveFile}
              onExtractSummary={handleExtractSummary}
            />
          )}
          
          {currentStep === 'summary' && (
            <DocumentSummaryStep
              documentSummary={documentSummary}
              selectedSections={selectedSections}
              originalContent={originalContent}
              isGeneratingPopularized={isGeneratingPopularized}
              onSectionSelection={setSelectedSections}
              onContinue={handleGeneratePopularized}
              onRestart={handleRestart}
            />
          )}
          
          {currentStep === 'result' && (
            <DocumentResultStep
              file={file}
              selectedFocusIds={selectedSections}
              popularizedContent={popularizedContent}
              originalContent={originalContent}
              onFileChange={handleFileChange}
              onRestart={handleRestart}
            />
          )}
        </div>
      </div>

      {/* 历史记录面板 */}
      <DocumentHistoryPanel
        historyItems={historyItems}
        isLoading={isLoadingHistory}
        isOpen={showHistory}
        onRefresh={fetchHistory}
        onClose={handleCloseHistory}
        onCopyContent={copyContent}
        onDeleteItem={deleteHistoryItem}
      />
    </div>
  );
};

export default DocumentConvert;
