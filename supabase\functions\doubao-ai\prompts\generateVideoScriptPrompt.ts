
export const generateVideoScriptPrompt = (content: string, platform: string = "", style: string = ""): string => {
  return `你现在是一名顶尖的法律短视频内容策划专家，尤其擅长将枯燥、复杂的法律法规，转化为适合在抖音、视频号等平台传播的，既能普法又能吸引潜在客户的纯口播短视频脚本。

原始内容：
${content}

平台：${platform || '通用短视频平台'}
风格：${style || '专业科普'}

请严格按照以下【6步结构】生成纯口播脚本内容：

【1. 爆款标题】(1-2个选项):
格式：使用提问式、反常识式或利益驱动式标题。

【2. 黄金3秒 (开场白)】(约5秒):
目标：抓住用户注意力，激发好奇心。
方法：直接抛出核心问题或一个惊人结论。

【3. 问题阐述与引入】(约15秒):
直接说明法律问题的普遍性和重要性。
用简洁的语言描述这类问题在现实中的常见情况。

【4. 法条解读与核心要点】(约25秒):
这是普法核心。用最简单的话解释相关法律规定。
必须包含"法律依据"环节，明确引用法律名称和关键条文号。
将复杂的法律概念转化为通俗易懂的表述。

【5. 划重点 (行动指南)】(约10秒):
给观众一个清晰、可执行的建议或总结。
格式：用"所以，你一定要记住…"或"一句话总结…"开头。

【6. 价值升华与转化引导 (CTA)】(约5秒):
建立专业形象，并给出互动钩子。

【7. 视频呈现建议】:
- 画面风格: 纯口播+图文大字报形式
- 关键字幕: 提炼出需要重点突出的大字卡
- 背景音乐(BGM): 建议紧张快节奏、轻松科普或沉稳专业风格
- 互动话题标签 (Hashtag): 提供3-5个相关热门标签

重要原则：
1. 直接切入：从法律问题的核心痛点开始，不绕弯子
2. 知识导向：重点在于传递清晰、准确的法律知识
3. 说人话：将法律术语替换成通俗易懂的大白话
4. 逻辑清晰：按照问题-解释-指导的逻辑顺序展开
5. 价值交付：明确告诉观众"应该怎么做"和"为什么这样做"
6. 专业转化：巧妙展示专业性，引导用户进行下一步行动

脚本风格要求：
- 语言简洁有力，避免冗长表述
- 多用短句，便于口播和理解
- 适当使用反问句增强互动感
- 重要信息用"注意"、"记住"等词强调

请在脚本结尾提醒添加免责声明："本视频仅为知识分享，不构成正式法律意见，具体问题请咨询专业律师。"

直接输出纯口播短视频脚本内容，不要添加任何开场白、结尾语或说明性文字：`;
};
