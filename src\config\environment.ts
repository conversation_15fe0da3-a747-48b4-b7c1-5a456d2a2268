export interface AppConfig {
  apiProvider: 'supabase' | 'volcano';
  volcanoApiUrl?: string;
  supabaseConfig?: {
    url: string;
    anonKey: string;
  };
}

export const getAppConfig = (): AppConfig => {
  // 支持环境变量配置
  const apiProvider = (import.meta.env.VITE_API_PROVIDER as 'supabase' | 'volcano') || 'supabase';
  
  const config: AppConfig = {
    apiProvider,
  };

  if (apiProvider === 'volcano') {
    config.volcanoApiUrl = import.meta.env.VITE_VOLCANO_API_URL || 'https://sd1mdl74gijnb7ntvsi00.apigateway-cn-guangzhou.volceapi.com';
  } else {
    // 保留原有Supabase配置作为备用
    config.supabaseConfig = {
      url: 'https://jgidblaeonxgfjavsnhy.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.RG455r7d1rSeY9V9cobz72caWQXHd5w20_FEpQrZHAA'
    };
  }

  return config;
};

// 全局配置实例
export const appConfig = getAppConfig();