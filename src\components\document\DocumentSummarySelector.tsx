
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { FileSearch, Scale, FileText } from 'lucide-react';
import { SummaryHeader } from './summary/SummaryHeader';
import { SelectionControls } from './summary/SelectionControls';
import { SectionGroup } from './summary/SectionGroup';

interface DisputePoint {
  序号: number;
  争议类型: string;
  争议描述: string;
  裁判要点: string;
}

interface DocumentSummary {
  structured_disputes?: DisputePoint[];
  raw_summary?: string;
  error?: string;
  suggestion?: string;
}

interface DocumentSummarySelectorProps {
  summary: DocumentSummary;
  selectedSections: string[];
  onSectionSelection: (sections: string[]) => void;
  onContinue: () => void;
  isGenerating: boolean;
}

export const DocumentSummarySelector: React.FC<DocumentSummarySelectorProps> = ({
  summary,
  selectedSections,
  onSectionSelection,
  onContinue,
  isGenerating
}) => {
  // 如果有结构化数据，使用新格式
  const structuredDisputes = summary.structured_disputes || [];
  
  // 为每个争议点创建一个选择项
  const sections = structuredDisputes.map((dispute, index) => ({
    key: `dispute_${dispute.序号}`,
    title: `${dispute.序号}. ${dispute.争议类型 === '事实争议' ? '事实争议' : '法律争议'}`,
    description: dispute.争议描述,
    content: `${dispute.争议描述}\n\n裁判要点：${dispute.裁判要点}`,
    icon: dispute.争议类型 === '事实争议' ? '📖' : '⚖️',
    type: dispute.争议类型
  }));

  // 如果没有结构化数据，则显示原始内容
  if (sections.length === 0 && summary.raw_summary) {
    sections.push({
      key: 'raw_content',
      title: '文档核心内容',
      description: '提取的文档要点信息',
      content: summary.raw_summary,
      icon: '📄',
      type: '综合内容'
    });
  }

  const handleSectionToggle = (sectionKey: string) => {
    if (selectedSections.includes(sectionKey)) {
      onSectionSelection(selectedSections.filter(key => key !== sectionKey));
    } else {
      onSectionSelection([...selectedSections, sectionKey]);
    }
  };

  const handleSelectAll = () => {
    if (selectedSections.length === sections.length) {
      onSectionSelection([]);
    } else {
      onSectionSelection(sections.map(section => section.key));
    }
  };

  // 按类型分组显示
  const factualDisputes = sections.filter(s => s.type === '事实争议');
  const legalDisputes = sections.filter(s => s.type === '法律争议');
  const otherSections = sections.filter(s => s.type !== '事实争议' && s.type !== '法律争议');

  return (
    <Card className="bg-white/90 backdrop-blur-lg shadow-2xl border-0 shadow-blue-100/30 rounded-3xl overflow-hidden">
      <SummaryHeader />
      
      <CardContent className="p-8 space-y-8">
        <SelectionControls
          selectedCount={selectedSections.length}
          totalCount={sections.length}
          onSelectAll={handleSelectAll}
          onContinue={onContinue}
          isGenerating={isGenerating}
        />

        <div className="space-y-8">
          <SectionGroup
            title="事实争议"
            sections={factualDisputes}
            icon={<FileSearch className="w-5 h-5 text-blue-600" />}
            selectedSections={selectedSections}
            onSectionToggle={handleSectionToggle}
          />
          
          <SectionGroup
            title="法律争议"
            sections={legalDisputes}
            icon={<Scale className="w-5 h-5 text-purple-600" />}
            selectedSections={selectedSections}
            onSectionToggle={handleSectionToggle}
          />
          
          <SectionGroup
            title="其他内容"
            sections={otherSections}
            icon={<FileText className="w-5 h-5 text-gray-600" />}
            selectedSections={selectedSections}
            onSectionToggle={handleSectionToggle}
          />
        </div>
      </CardContent>
    </Card>
  );
};
