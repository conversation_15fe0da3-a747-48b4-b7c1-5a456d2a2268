import { useState, useEffect, useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { aiService } from "@/services/aiServiceFactory";
import { useAuth } from "@/contexts/AuthContext";
import { useGenerationHistory } from "@/hooks/useGenerationHistory";
import TopicsHeader from "@/components/topics/TopicsHeader";
import AnimatedLoadingList from "@/components/AnimatedLoadingList";
import TopicsList from "@/components/topics/TopicsList";
import EmptyState from "@/components/topics/EmptyState";
import StreamingContentCard from "@/components/topics/StreamingContentCard";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

const Topics = () => {
  const { domain } = useParams<{ domain: string }>();
  const [topics, setTopics] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState<string[]>([]);
  const [streamingContent, setStreamingContent] = useState<string[]>([]);
  const [currentGeneratingIndex, setCurrentGeneratingIndex] = useState(-1);
  const [currentDomainInfo, setCurrentDomainInfo] = useState<any>(null);
  const { user } = useAuth();
  const { saveHistory } = useGenerationHistory();

  // 使用 ref 来避免不必要的重新渲染
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的内容更新函数
  const debouncedUpdateStreamingContent = useCallback((index: number, content: string) => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    updateTimeoutRef.current = setTimeout(() => {
      setStreamingContent(prev => {
        if (prev[index] !== content) {
          const newContent = [...prev];
          newContent[index] = content;
          return newContent;
        }
        return prev;
      });
    }, 50); // 50ms 防抖延迟
  }, []);

  const domainMap: Record<string, any> = {
    marriage: { title: "婚姻家庭", tag: "婚姻财产", icon: "💝" },
    labor: { title: "劳动用工", tag: "劳动法律", icon: "💼" },
    debt: { title: "债务纠纷", tag: "债务追讨", icon: "💳" },
    property: { title: "房产纠纷", tag: "房产法律", icon: "🏠" },
    corporate: { title: "公司法务", tag: "公司法律", icon: "🏢" },
    consumer: { title: "消费维权", tag: "消费者权益", icon: "🛡️" },
    criminal: { title: "刑事辩护", tag: "刑事法律", icon: "⚖️" },
    contract: { title: "合同审查", tag: "合同法律", icon: "📄" }
  };

  useEffect(() => {
    if (domain && domainMap[domain]) {
      setCurrentDomainInfo(domainMap[domain]);
      generateTopics();
    }
  }, [domain]);

  const generateTopics = async () => {
    if (!domain) return;
    
    setIsGenerating(true);
    setTopics([]);
    
    try {
      const generatedTopics = await aiService.generateTopics(domain);
      setTopics(generatedTopics);
      toast({
        title: "选题生成完成",
        description: `已为您生成${generatedTopics.length}个热门选题`,
      });
    } catch (error) {
      console.error('生成选题失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "生成过程中出现错误",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // 并发处理单个选题的生成（带重试机制）
  const generateSingleTopic = async (
    topic: string,
    index: number,
    retryCount: number = 0,
    maxRetries: number = 2
  ): Promise<{
    index: number;
    topic: string;
    content: string;
    success: boolean;
    error?: string;
    retryCount: number;
  }> => {
    try {
      console.log(`开始生成选题 ${index + 1}: "${topic}"${retryCount > 0 ? ` (重试 ${retryCount}/${maxRetries})` : ''}`);

      let fullContent = '';

      // 添加超时控制
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('生成超时')), 60000); // 60秒超时
      });

      const generatePromise = aiService.generateContentByTopicStream(topic, (content) => {
        fullContent = content;
        // 使用防抖更新，减少渲染频率
        debouncedUpdateStreamingContent(index, content);
      });

      await Promise.race([generatePromise, timeoutPromise]);

      // 验证生成的内容
      if (!fullContent || fullContent.trim().length < 10) {
        throw new Error('生成的内容过短或为空');
      }

      // 保存历史记录
      if (domain && user && fullContent) {
        try {
          await saveHistory(domain, topic, fullContent);
          console.log(`选题 ${index + 1} 历史记录保存成功`);
        } catch (error) {
          console.error(`保存第 ${index + 1} 条选题历史记录失败:`, error);
          // 历史记录保存失败不影响主流程
        }
      }

      console.log(`选题 ${index + 1} 生成成功，内容长度: ${fullContent.length}`);
      return {
        index,
        topic,
        content: fullContent,
        success: true,
        retryCount
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '生成失败';
      console.error(`生成第 ${index + 1} 条选题失败:`, errorMessage);

      // 如果还有重试次数，则重试
      if (retryCount < maxRetries) {
        console.log(`准备重试选题 ${index + 1}，当前重试次数: ${retryCount + 1}/${maxRetries}`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
        return generateSingleTopic(topic, index, retryCount + 1, maxRetries);
      }

      return {
        index,
        topic,
        content: '',
        success: false,
        error: errorMessage,
        retryCount
      };
    }
  };

  // 分批并发处理选题
  const processBatch = async (topics: string[], startIndex: number, batchSize: number = 3) => {
    const batch = topics.slice(startIndex, startIndex + batchSize);
    const batchPromises = batch.map((topic, batchIndex) =>
      generateSingleTopic(topic, startIndex + batchIndex)
    );

    return await Promise.allSettled(batchPromises);
  };

  const handleGenerateSelectedTopics = async (selectedTopics: string[]) => {
    setSelectedTopics(selectedTopics);
    setIsGeneratingContent(true);
    setGeneratedContent([]);
    setStreamingContent(new Array(selectedTopics.length).fill(''));
    setCurrentGeneratingIndex(0);

    try {
      const allContents: string[] = new Array(selectedTopics.length).fill('');
      const batchSize = 3; // 并发数量限制
      let successCount = 0;
      let failureCount = 0;
      const failedTopics: string[] = [];

      // 分批处理所有选题
      for (let i = 0; i < selectedTopics.length; i += batchSize) {
        console.log(`处理批次 ${Math.floor(i / batchSize) + 1}，选题 ${i + 1}-${Math.min(i + batchSize, selectedTopics.length)}`);

        const batchResults = await processBatch(selectedTopics, i, batchSize);

        // 处理批次结果
        batchResults.forEach((result, batchIndex) => {
          if (result.status === 'fulfilled') {
            const { index, content, success, topic, error, retryCount } = result.value;
            if (success && content) {
              allContents[index] = content;
              successCount++;
              console.log(`✅ 选题 ${index + 1} "${topic}" 生成成功${retryCount > 0 ? ` (经过 ${retryCount} 次重试)` : ''}`);
            } else {
              failureCount++;
              failedTopics.push(`${topic}${retryCount > 0 ? ` (重试${retryCount}次后失败)` : ''}`);
              console.error(`❌ 选题 "${topic}" 最终生成失败:`, error);
            }
          } else {
            failureCount++;
            const topicIndex = i + batchIndex;
            if (topicIndex < selectedTopics.length) {
              const failedTopic = selectedTopics[topicIndex];
              failedTopics.push(`${failedTopic} (系统错误)`);
              console.error(`❌ 选题 "${failedTopic}" 系统处理失败:`, result.reason);
            }
          }
        });

        // 更新进度 - 使用防抖逻辑，避免频繁更新
        const completedCount = Math.min(i + batchSize, selectedTopics.length);
        if (completedCount < selectedTopics.length) {
          setCurrentGeneratingIndex(completedCount - 1);
        }
      }

      // 过滤掉空内容，只保留成功生成的内容
      const validContents = allContents.filter(content => content.trim() !== '');
      setGeneratedContent(validContents);
      setCurrentGeneratingIndex(-1);

      // 显示结果通知
      if (successCount > 0) {
        const successRate = Math.round((successCount / selectedTopics.length) * 100);

        if (failureCount === 0) {
          // 全部成功
          toast({
            title: "🎉 文案生成完成",
            description: `已为全部${successCount}个选题生成专业文案`,
          });
        } else if (successCount > failureCount) {
          // 大部分成功
          toast({
            title: "✅ 文案生成基本完成",
            description: `成功生成${successCount}条文案，${failureCount}条失败 (成功率: ${successRate}%)`,
          });

          // 显示失败详情
          console.group('📋 生成结果详情');
          console.log(`✅ 成功: ${successCount}条`);
          console.log(`❌ 失败: ${failureCount}条`);
          console.log('失败的选题:', failedTopics);
          console.groupEnd();
        } else {
          // 失败较多
          toast({
            title: "⚠️ 部分文案生成成功",
            description: `成功生成${successCount}条文案，${failureCount}条失败，建议重试失败的选题`,
            variant: "destructive"
          });
        }
      } else {
        console.error('所有选题生成都失败了:', failedTopics);
        throw new Error(`所有选题生成都失败了。失败原因可能包括：网络连接问题、API限制或内容过滤。请检查网络连接并稍后重试。`);
      }

    } catch (error) {
      console.error('生成文案失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "生成过程中出现错误",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingContent(false);
      setCurrentGeneratingIndex(-1);
    }
  };

  const handleBackToTopics = useCallback(() => {
    setGeneratedContent([]);
    setSelectedTopics([]);
    setStreamingContent([]);
    setCurrentGeneratingIndex(-1);
  }, []);

  if (!currentDomainInfo) {
    return <div>加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/70 via-white to-indigo-50/70">
      <TopicsHeader currentDomainInfo={currentDomainInfo} domain={domain} />

      <div className="max-w-6xl mx-auto px-6 py-12">
        {isGenerating ? (
          <AnimatedLoadingList
            title={`正在生成${currentDomainInfo.title}热门选题...`}
            description={`AI正在为您分析${currentDomainInfo.title}领域的热门话题`}
          />
        ) : isGeneratingContent || generatedContent.length > 0 ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {selectedTopics.map((_, index) => (
                <StreamingContentCard
                  key={index}
                  content={streamingContent[index] || generatedContent[index] || ''}
                  index={index}
                  currentDomainInfo={currentDomainInfo}
                  isGenerating={isGeneratingContent}
                  currentGeneratingIndex={currentGeneratingIndex}
                  totalTopics={selectedTopics.length}
                />
              ))}
            </div>

            <div className="flex flex-col items-center space-y-4 pt-8">
              <Button 
                onClick={handleBackToTopics} 
                variant="outline" 
                size="lg" 
                className="px-8 py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm border-gray-200/70 hover:border-blue-300 hover:bg-blue-50/50"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                返回选题列表
              </Button>
            </div>
          </div>
        ) : topics.length > 0 ? (
          <TopicsList
            topics={topics}
            currentDomainInfo={currentDomainInfo}
            domain={domain!}
            onGenerateSelectedTopics={handleGenerateSelectedTopics}
          />
        ) : (
          <EmptyState
            currentDomainInfo={currentDomainInfo}
            onGenerateTopics={generateTopics}
          />
        )}
      </div>
    </div>
  );
};

export default Topics;
