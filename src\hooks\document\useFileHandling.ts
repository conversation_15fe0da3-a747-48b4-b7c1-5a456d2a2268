import { useFileValidation } from './useFileValidation';

export const useFileHandling = (
  updateState: (updates: any) => void,
  resetState: () => void
) => {
  const { validateSelectedFile, showSuccessToast } = useFileValidation();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (!validateSelectedFile(selectedFile)) {
        return;
      }
      
      // Reset all state to initial state, but keep the new file
      resetState();
      updateState({ file: selectedFile });

      showSuccessToast(
        "文件已选择",
        `已选择文件：${selectedFile.name}`
      );
    }
  };

  const handleRemoveFile = () => {
    resetState();
    
    // Clear file input
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const clearFileInput = () => {
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  return {
    handleFileChange,
    handleRemoveFile,
    clearFileInput
  };
};
