
import { useDocumentState } from './document/useDocumentState';
import { useFileHandling } from './document/useFileHandling';
import { useDocumentProcessing } from './document/useDocumentProcessing';
import { useToast } from '@/hooks/use-toast';

export const useDocumentConvert = () => {
  const {
    state,
    updateState,
    resetState,
    setDocumentSummary,
    setSelectedSections,
    setPopularizedContent
  } = useDocumentState();

  const { toast } = useToast();

  const { handleFileChange, handleRemoveFile, clearFileInput } = useFileHandling(
    updateState,
    resetState
  );

  const { handleExtractSummary, handleGeneratePopularized } = useDocumentProcessing(
    state,
    updateState,
    setDocumentSummary,
    setPopularizedContent
  );

  // Restart the entire process
  const handleRestart = () => {
    resetState();
    clearFileInput();

    toast({
      title: "已重置",
      description: "已清空所有内容，可以开始新的文档转换",
    });
  };

  return {
    ...state,
    handleFileChange,
    handleRemoveFile,
    handleExtractSummary,
    handleGeneratePopularized,
    setSelectedSections,
    handleRestart
  };
};
