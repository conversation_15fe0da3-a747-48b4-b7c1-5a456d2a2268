# 火山引擎函数部署指南

## 前置条件

1. 确保您有火山引擎账号并开通了函数计算服务
2. 安装火山引擎CLI工具
3. 配置好API网关

## 环境变量配置

在火山引擎函数计算控制台中，为每个函数配置以下环境变量：

### textin-convert 函数
- `TEXTIN_APP_ID`: 您的Textin API应用ID
- `TEXTIN_SECRET_CODE`: 您的Textin API密钥

### ai-service 函数
- `DOUBAO_API_KEY`: 您的豆包API密钥

## 部署步骤

### 1. 部署 textin-convert 函数

```bash
cd volcano-functions/textin-convert
npm install
# 打包并上传到火山引擎函数计算
```

### 2. 部署 ai-service 函数

```bash
cd volcano-functions/ai-service
npm install
# 打包并上传到火山引擎函数计算
```

### 3. 配置API网关

在火山引擎API网关中创建以下路由：

- `POST /textin-convert` -> textin-convert函数
- `POST /ai-service` -> ai-service函数

确保启用CORS支持。

## 测试

部署完成后，您可以使用以下方式测试：

### 测试文档转换
```bash
curl -X POST https://your-api-gateway-url/textin-convert \
  -F "file=@test.pdf"
```

### 测试AI服务
```bash
curl -X POST https://your-api-gateway-url/ai-service \
  -H "Content-Type: application/json" \
  -d '{"action": "generateTopics", "data": {"domain": "法律"}}'
```

## 注意事项

1. 确保函数的超时时间设置足够长（建议300秒）
2. 内存配置建议至少512MB
3. 确保网络配置允许访问外部API
4. 监控函数日志以便调试

## 故障排除

如果遇到问题，请检查：

1. 环境变量是否正确配置
2. 函数日志中的错误信息
3. API网关的路由配置
4. CORS设置是否正确
