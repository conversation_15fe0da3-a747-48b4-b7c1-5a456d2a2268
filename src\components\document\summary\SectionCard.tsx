
import React from 'react';
import { CheckCircle2, Circle } from 'lucide-react';

interface SectionCardProps {
  section: {
    key: string;
    title: string;
    description: string;
    content: string;
    icon: string;
    type: string;
  };
  isSelected: boolean;
  onToggle: (sectionKey: string) => void;
}

export const SectionCard: React.FC<SectionCardProps> = ({
  section,
  isSelected,
  onToggle
}) => {
  return (
    <div
      className={`group relative overflow-hidden rounded-3xl border-2 transition-all duration-300 cursor-pointer ${
        isSelected
          ? 'border-blue-400 bg-gradient-to-br from-blue-50/80 to-purple-50/40 shadow-lg shadow-blue-200/30'
          : 'border-gray-200/60 hover:border-blue-300 hover:bg-gradient-to-br hover:from-blue-50/50 hover:to-purple-50/20 hover:shadow-md'
      }`}
      onClick={() => onToggle(section.key)}
    >
      {isSelected && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-600"></div>
      )}
      
      <div className="p-6">
        <div className="flex items-start space-x-4">
          <div className="relative mt-1">
            <div className={`w-6 h-6 rounded-xl border-2 transition-all duration-300 flex items-center justify-center ${
              isSelected
                ? 'border-blue-500 bg-gradient-to-br from-blue-500 to-purple-600 shadow-md'
                : 'border-gray-300 group-hover:border-blue-400 bg-white'
            }`}>
              {isSelected && (
                <CheckCircle2 className="w-4 h-4 text-white" />
              )}
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-3">
              <span className="text-2xl">{section.icon}</span>
              <h4 className="font-bold text-gray-900 text-lg">
                {section.title}
              </h4>
            </div>
            
            <p className="text-sm text-gray-600 mb-4 leading-relaxed">
              {section.description}
            </p>
            
            <div className="relative">
              <div className="text-sm text-gray-700 bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-200/50 max-h-48 overflow-y-auto shadow-inner">
                {section.content ? (
                  <div className="whitespace-pre-wrap break-words leading-relaxed">
                    {section.content}
                  </div>
                ) : (
                  <div className="text-gray-500 italic flex items-center justify-center py-8">
                    <Circle className="w-4 h-4 mr-2" />
                    暂无内容
                  </div>
                )}
              </div>
              
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white/70 to-transparent pointer-events-none rounded-b-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
