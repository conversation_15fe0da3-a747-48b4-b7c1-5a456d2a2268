
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 自定义动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes progressFill {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(0.8);
  }
  50% {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 加载项动画类 */
.loading-item-enter {
  animation: fadeInUp 0.6s ease-out forwards;
}

.loading-item-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.loading-progress {
  animation: progressFill 2s ease-in-out infinite;
}

.loading-float {
  animation: float 3s ease-in-out infinite;
}

/* 悬停效果增强 */
.loading-item {
  position: relative;
  overflow: hidden;
}

.loading-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(59, 130, 246, 0.1), 
    transparent
  );
  transition: left 0.6s ease;
}

.loading-item:hover::before {
  left: 100%;
}
