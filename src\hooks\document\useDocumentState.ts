
import { useState } from 'react';
import { DocumentConvertState, DocumentStep, DocumentSummary } from '@/types/documentConvert';

export const useDocumentState = () => {
  const [state, setState] = useState<DocumentConvertState>({
    file: null,
    isProcessing: false,
    isExtractingSummary: false,
    isGeneratingPopularized: false,
    originalContent: '',
    documentSummary: null,
    selectedSections: [],
    popularizedContent: '',
    currentStep: 'upload'
  });

  const updateState = (updates: Partial<DocumentConvertState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const resetState = () => {
    setState({
      file: null,
      isProcessing: false,
      isExtractingSummary: false,
      isGeneratingPopularized: false,
      originalContent: '',
      documentSummary: null,
      selectedSections: [],
      popularizedContent: '',
      currentStep: 'upload'
    });
  };

  const setStep = (step: DocumentStep) => {
    updateState({ currentStep: step });
  };

  const setDocumentSummary = (summary: DocumentSummary) => {
    updateState({ documentSummary: summary, currentStep: 'summary' });
  };

  const setSelectedSections = (sections: string[]) => {
    updateState({ selectedSections: sections });
  };

  const setPopularizedContent = (content: string) => {
    updateState({ popularizedContent: content, currentStep: 'result' });
  };

  return {
    state,
    updateState,
    resetState,
    setStep,
    setDocumentSummary,
    setSelectedSections,
    setPopularizedContent
  };
};
