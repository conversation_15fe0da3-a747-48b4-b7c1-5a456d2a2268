
import React from 'react';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';

interface SelectionControlsProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onContinue: () => void;
  isGenerating: boolean;
}

export const SelectionControls: React.FC<SelectionControlsProps> = ({
  selectedCount,
  totalCount,
  onSelectAll,
  onContinue,
  isGenerating
}) => {
  return (
    <>
      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl border border-gray-100">
        <Button
          variant="outline"
          size="sm"
          onClick={onSelectAll}
          className="flex items-center space-x-3 rounded-2xl border-2 border-blue-200/70 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 px-6 py-3 shadow-sm hover:shadow-lg hover:shadow-blue-200/30"
        >
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-md">
            <Sparkles className="w-3.5 h-3.5" />
          </div>
          <span className="text-sm font-semibold text-gray-700">
            {selectedCount === totalCount ? '取消全选' : '全选'}
          </span>
        </Button>
        
        <div className="flex items-center space-x-3 bg-white px-4 py-2 rounded-2xl border border-blue-200/50 shadow-sm">
          <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
          <span className="text-sm text-blue-700 font-medium">
            已选择 {selectedCount} / {totalCount} 个部分
          </span>
        </div>
      </div>

      <div className="pt-6 border-t border-gray-100">
        <Button
          onClick={onContinue}
          disabled={selectedCount === 0 || isGenerating}
          className="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600 hover:from-blue-600 hover:via-purple-600 hover:to-blue-700 text-white font-semibold py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] disabled:transform-none disabled:opacity-50"
        >
          {isGenerating ? (
            <div className="flex items-center space-x-3">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>AI正在生成科普文案...</span>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Sparkles className="w-5 h-5" />
              <span>生成选中争议点的科普文案 ({selectedCount}个部分)</span>
            </div>
          )}
        </Button>
      </div>
    </>
  );
};
