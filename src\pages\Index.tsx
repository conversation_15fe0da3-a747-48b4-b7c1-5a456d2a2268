import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import ApiKeyInput from "@/components/ApiKeyInput";
import Header from "@/components/layout/Header";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Sparkles, FileText, BookOpen } from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // 如果用户未登录，显示服务准备界面
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <ApiKeyInput onApiKeySet={() => {}} />
      </div>
    );
  }

  const features = [
    {
      id: "optimize",
      title: "内容优化",
      description: "输入您的原始文案，AI将为您进行专业优化",
      icon: Sparkles,
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50/80 via-indigo-50/60 to-blue-100/40",
      path: "/optimize"
    },
    {
      id: "generate",
      title: "内容生成",
      description: "选择法律领域，AI为您生成专业营销文案",
      icon: FileText,
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50/80 via-emerald-50/60 to-green-100/40",
      path: "/content-generate"
    },
    {
      id: "document",
      title: "判决书科普",
      description: "上传法律文档，AI转换为易懂的科普内容",
      icon: BookOpen,
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50/80 via-violet-50/60 to-purple-100/40",
      path: "/document-convert"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header />

      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">法律文案助手</h2>
          <p className="text-lg text-gray-600">选择您需要的AI功能，开始创作专业法律文案</p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature) => {
            const IconComponent = feature.icon;
            
            return (
              <Card
                key={feature.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-2xl transform hover:scale-105 hover:-translate-y-2 border-0 bg-gradient-to-br ${feature.bgColor} backdrop-blur-sm rounded-3xl overflow-hidden shadow-lg`}
                onClick={() => navigate(feature.path)}
              >
                <CardContent className="p-8 text-center h-full flex flex-col justify-between">
                  <div>
                    <div className={`w-20 h-20 bg-gradient-to-br ${feature.color} rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl transition-transform duration-300 hover:scale-110`}>
                      <IconComponent className="w-10 h-10 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                    <p className="text-sm text-gray-600 leading-relaxed mb-6">{feature.description}</p>
                  </div>
                  
                  <Button 
                    className={`w-full bg-gradient-to-r ${feature.color} text-white py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg justify-center`}
                  >
                    立即开始
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Index;
