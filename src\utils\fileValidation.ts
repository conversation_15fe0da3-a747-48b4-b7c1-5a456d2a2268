
export const validateFile = (file: File): { isValid: boolean; error?: string } => {
  const fileType = file.type;
  const validTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  
  const isValidType = validTypes.includes(fileType) || 
    file.name.toLowerCase().endsWith('.pdf') || 
    file.name.toLowerCase().endsWith('.doc') || 
    file.name.toLowerCase().endsWith('.docx');
  
  if (!isValidType) {
    return { isValid: false, error: '请上传PDF或Word文档' };
  }
  
  // 检查文件大小（500MB限制，符合Textin API规范）
  if (file.size > 500 * 1024 * 1024) {
    return { isValid: false, error: '文件大小不能超过500MB，请选择较小的文件' };
  }
  
  return { isValid: true };
};

export const getErrorMessage = (error: Error): { title: string; message: string } => {
  const errorMsg = error.message;
  
  if (errorMsg.includes('文档格式不兼容')) {
    return {
      title: "文档格式不兼容",
      message: "该文档格式可能过于陈旧或已损坏。建议尝试以下解决方案：\n1. 使用较新版本的Word重新保存为.docx格式\n2. 将文档转换为PDF格式后重新上传\n3. 检查文档是否有密码保护并先解除保护"
    };
  } else if (errorMsg.includes('文件过大')) {
    return {
      title: "文件过大",
      message: "文件大小超过限制，请选择较小的文件"
    };
  } else if (errorMsg.includes('文档解析服务暂时不可用')) {
    return {
      title: "服务暂时不可用",
      message: "文档解析服务暂时不可用，请稍后重试"
    };
  } else if (errorMsg.includes('请求过于频繁')) {
    return {
      title: "请求过于频繁",
      message: "请求过于频繁，请稍后重试"
    };
  } else if (errorMsg.includes('Edge Function returned a non-2xx status code')) {
    return {
      title: "解析服务异常",
      message: "文档解析服务出现异常，请稍后重试或联系技术支持"
    };
  } else {
    return {
      title: "文档解析失败",
      message: errorMsg
    };
  }
};
