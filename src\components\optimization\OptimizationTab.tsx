import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useIsMobile } from '@/hooks/use-mobile';
import OptimizationInput from './OptimizationInput';
import MultiResultTabs from './MultiResultTabs';
import OptimizationHistoryPanel from './OptimizationHistoryPanel';

interface OptimizationTabProps {
  originalText: string;
  optimizedText: string;
  videoScript: string;
  platform: string;
  style: string;
  contentTypes: string[];
  isOptimizing: boolean;
  hasGenerated: boolean;
  onOriginalTextChange: (text: string) => void;
  onPlatformChange: (platform: string) => void;
  onStyleChange: (style: string) => void;
  onContentTypesChange: (types: string[]) => void;
  onOptimize: () => void;
  onCopyOptimizedText: () => void;
  onOptimizedTextChange?: (text: string) => void;
  onVideoScriptChange?: (text: string) => void;
}

const OptimizationTab: React.FC<OptimizationTabProps> = ({
  originalText,
  optimizedText,
  videoScript,
  platform,
  style,
  contentTypes,
  isOptimizing,
  hasGenerated,
  onOriginalTextChange,
  onPlatformChange,
  onStyleChange,
  onContentTypesChange,
  onOptimize,
  onCopyOptimizedText,
  onOptimizedTextChange,
  onVideoScriptChange
}) => {
  const isMobile = useIsMobile();

  const results = {
    copywriting: contentTypes.includes('copywriting') ? optimizedText : undefined,
    videoScript: contentTypes.includes('video-script') ? videoScript : undefined
  };

  if (isMobile) {
    return (
      <div className="px-4 pb-6">
        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-lg rounded-2xl overflow-hidden">
          <CardContent className="p-6">
            <div className="space-y-6">
              {/* Input Section */}
              <OptimizationInput
                originalText={originalText}
                platform={platform}
                style={style}
                contentTypes={contentTypes}
                isOptimizing={isOptimizing}
                hasGenerated={hasGenerated}
                onOriginalTextChange={onOriginalTextChange}
                onPlatformChange={onPlatformChange}
                onStyleChange={onStyleChange}
                onContentTypesChange={onContentTypesChange}
                onOptimize={onOptimize}
              />

              {/* Output Section */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">
                    生成结果
                  </label>
                  <OptimizationHistoryPanel />
                </div>
                <MultiResultTabs
                  results={results}
                  isGenerating={isOptimizing}
                  selectedTypes={contentTypes}
                  originalText={originalText}
                  onCopywritingChange={onOptimizedTextChange}
                  onVideoScriptChange={onVideoScriptChange}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // PC端布局
  return (
    <div>
      <Card className="max-w-5xl mx-auto shadow-2xl border-0 bg-white/90 backdrop-blur-lg rounded-3xl overflow-hidden">
        <CardContent className="p-10">
          <div className="grid md:grid-cols-2 gap-10">
            {/* Input Section */}
            <OptimizationInput
              originalText={originalText}
              platform={platform}
              style={style}
              contentTypes={contentTypes}
              isOptimizing={isOptimizing}
              hasGenerated={hasGenerated}
              onOriginalTextChange={onOriginalTextChange}
              onPlatformChange={onPlatformChange}
              onStyleChange={onStyleChange}
              onContentTypesChange={onContentTypesChange}
              onOptimize={onOptimize}
            />

            {/* Output Section */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  生成结果
                </label>
                <OptimizationHistoryPanel />
              </div>
              <MultiResultTabs
                results={results}
                isGenerating={isOptimizing}
                selectedTypes={contentTypes}
                originalText={originalText}
                onCopywritingChange={onOptimizedTextChange}
                onVideoScriptChange={onVideoScriptChange}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OptimizationTab;
