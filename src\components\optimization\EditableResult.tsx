
import React, { useState, useRef, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Copy } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface EditableResultProps {
  content: string;
  isGenerating: boolean;
  contentType: 'copywriting' | 'video-script';
  onContentChange: (content: string) => void;
  onCopy: () => void;
  showCopyButton?: boolean;
}

const EditableResult: React.FC<EditableResultProps> = ({
  content,
  isGenerating,
  contentType,
  onContentChange,
  onCopy,
  showCopyButton = true
}) => {
  // 格式化短视频脚本内容，将小标题变成蓝色加粗
  const formatVideoScriptContent = (text: string) => {
    if (contentType !== 'video-script') return text;
    
    const lines = text.split('\n');
    const formattedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 匹配小标题格式：数字. 标题内容 或 【数字. 标题内容】
      const titleMatch = line.match(/^(\d+\.\s*[^】]*?)(?:】|[:：]|$)/) || line.match(/^【(\d+\.\s*[^】]*?)】/);
      
      if (titleMatch) {
        // 如果前面有内容，添加适当间距
        if (formattedLines.length > 0) {
          formattedLines.push(<div key={`spacer-${i}`} className="mb-3"></div>);
        }
        
        formattedLines.push(
          <div key={i} className="mb-1">
            <span className="text-blue-600 font-bold">{titleMatch[1]}</span>
            {line.substring(titleMatch[0].length)}
          </div>
        );
      } else if (line.trim()) {
        formattedLines.push(<div key={i} className="mb-1 ml-0">{line}</div>);
      } else {
        // 空行处理 - 只在非标题后添加小间距
        if (i > 0 && formattedLines.length > 0) {
          formattedLines.push(<div key={i} className="mb-1"></div>);
        }
      }
    }
    
    return formattedLines;
  };
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setEditContent(content);
  }, [content]);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.setSelectionRange(textareaRef.current.value.length, textareaRef.current.value.length);
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
    setEditContent(content);
  };

  const handleSave = () => {
    onContentChange(editContent);
    setIsEditing(false);
    toast({
      title: "保存成功",
      description: `${contentType === 'copywriting' ? '文案' : '短视频脚本'}已更新`,
    });
  };

  const handleCancel = () => {
    setEditContent(content);
    setIsEditing(false);
  };

  const handleBlur = () => {
    // 点击外部时自动保存
    handleSave();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSave();
    }
  };

  if (!content && !isGenerating) {
    return (
      <div className="text-gray-400 text-center py-24">
        <div className="w-12 h-12 mx-auto mb-4 opacity-30">
          {contentType === 'copywriting' ? '📝' : '🎬'}
        </div>
        <p className="text-lg">
          {contentType === 'copywriting' ? '生成的文案将在这里显示' : '短视频脚本将在这里显示'}
        </p>
      </div>
    );
  }

  if (isGenerating) {
    return (
      <div className="text-center py-24">
        <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p className="text-gray-500">
          正在生成{contentType === 'copywriting' ? '文案' : '短视频脚本'}...
        </p>
      </div>
    );
  }

  return (
    <>
      {showCopyButton && (
        <div className="flex items-center justify-end mb-4">
          <Button
            onClick={onCopy}
            size="icon"
            variant="outline"
            className="h-8 w-8"
            title="复制内容"
          >
            <Copy className="w-4 h-4" />
          </Button>
        </div>
      )}

      {isEditing ? (
        <Textarea
          ref={textareaRef}
          value={editContent}
          onChange={(e) => setEditContent(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          className="min-h-[280px] resize-none border-2 border-blue-300 focus:border-blue-500 transition-colors"
          placeholder={`请输入${contentType === 'copywriting' ? '文案' : '短视频脚本'}内容...`}
        />
      ) : (
        <div 
          className="text-gray-800 leading-relaxed cursor-pointer hover:bg-gray-50/50 p-3 rounded transition-colors min-h-[280px] border border-transparent hover:border-gray-200"
          onClick={handleEdit}
          title="点击编辑内容"
        >
          {contentType === 'video-script' ? formatVideoScriptContent(content) : <div className="whitespace-pre-wrap">{content}</div>}
        </div>
      )}

      {isEditing && (
        <div className="mt-2 text-xs text-gray-500">
          <p>点击外部区域保存，Esc 取消编辑</p>
        </div>
      )}
    </>
  );
};

export default EditableResult;
