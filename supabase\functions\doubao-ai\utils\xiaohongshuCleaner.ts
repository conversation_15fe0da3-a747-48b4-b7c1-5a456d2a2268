
import { removeSystemPrompts, removeSpecialSymbols, normalizeWhitespace } from './basicTextCleaning.ts';
import { removeMarkdownButKeepHashtags } from './markdownCleaning.ts';

// Specialized function for cleaning Xiaohongshu content generation - version that preserves hashtags
export function cleanXiaohongshuContent(text: string): string {
  if (!text) return '';
  
  // Remove system prompts first
  let cleanedText = removeSystemPrompts(text);
  
  // Clean markdown format symbols, but preserve # symbols in hashtags
  cleanedText = removeMarkdownButKeepHashtags(cleanedText);
  
  // Remove special symbols and decorative elements
  cleanedText = removeSpecialSymbols(cleanedText);
  
  // Remove multiple consecutive asterisks
  cleanedText = cleanedText.replace(/\*{2,}/g, '');
  
  // Normalize whitespace and line breaks
  cleanedText = normalizeWhitespace(cleanedText);
  
  // If cleaned content is empty or too short, return error message
  if (cleanedText.length < 50) {
    return '生成的内容需要进一步处理，请重新生成。';
  }
  
  return cleanedText;
}
