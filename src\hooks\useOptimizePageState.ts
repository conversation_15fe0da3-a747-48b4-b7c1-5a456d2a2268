
import { useState, useRef, useEffect } from 'react';

export interface OptimizePageState {
  originalText: string;
  optimizedText: string;
  videoScript: string;
  platform: string;
  style: string;
  contentTypes: string[];
  isOptimizing: boolean;
  hasGenerated: boolean;
  initialOriginalText: string;
}

export const useOptimizePageState = () => {
  const [originalText, setOriginalText] = useState("");
  const [optimizedText, setOptimizedText] = useState("");
  const [videoScript, setVideoScript] = useState("");
  const [platform, setPlatform] = useState("");
  const [style, setStyle] = useState("xiaohongshu");
  const [contentTypes, setContentTypes] = useState<string[]>(['copywriting']);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [initialOriginalText, setInitialOriginalText] = useState("");

  // 使用ref来保存最新的状态值，确保事件处理器能访问到最新值
  const stateRef = useRef({
    originalText,
    optimizedText,
    videoScript,
    platform,
    style,
    contentTypes
  });

  // 自动更新 stateRef，不需要手动调用
  useEffect(() => {
    stateRef.current = {
      originalText,
      optimizedText,
      videoScript,
      platform,
      style,
      contentTypes
    };
  }, [originalText, optimizedText, videoScript, platform, style, contentTypes]);

  const state: OptimizePageState = {
    originalText,
    optimizedText,
    videoScript,
    platform,
    style,
    contentTypes,
    isOptimizing,
    hasGenerated,
    initialOriginalText
  };

  const actions = {
    setOriginalText,
    setOptimizedText,
    setVideoScript,
    setPlatform,
    setStyle,
    setContentTypes,
    setIsOptimizing,
    setHasGenerated,
    setInitialOriginalText
  };

  return {
    state,
    actions,
    stateRef
  };
};
