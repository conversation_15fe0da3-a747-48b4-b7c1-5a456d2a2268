
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ApiKeyInputProps {
  onApiKeySet: () => void;
}

const ApiKeyInput = ({ onApiKeySet }: ApiKeyInputProps) => {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    
    try {
      // 模拟连接检查
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "连接成功",
        description: "AI服务已准备就绪，可以开始使用",
      });
      
      onApiKeySet();
    } catch (error) {
      toast({
        title: "连接失败",
        description: "无法连接到AI服务，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <Card className="max-w-md mx-auto shadow-lg border-0 bg-white/80 backdrop-blur">
      <CardContent className="p-6">
        <div className="text-center mb-6">
          <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">AI服务准备中</h2>
          <p className="text-sm text-gray-600">
            我们正在为您准备AI功能，无需任何配置
          </p>
        </div>

        <div className="space-y-4">
          <div className="bg-gradient-to-br from-blue-50/80 to-indigo-50/50 rounded-3xl p-6 backdrop-blur-sm border border-blue-100/50">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">服务特点</h3>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>无需配置API密钥</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>专业法律文案优化</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>智能内容生成</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>多平台适配</span>
              </li>
            </ul>
          </div>

          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white py-4 rounded-2xl font-medium text-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
          >
            {isConnecting ? "连接中..." : "开始使用"}
          </Button>

          <div className="text-xs text-gray-500 space-y-1">
            <p>• AI服务由后端统一管理</p>
            <p>• 安全可靠，开箱即用</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiKeyInput;
