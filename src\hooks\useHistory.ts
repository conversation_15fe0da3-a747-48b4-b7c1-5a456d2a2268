
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { saveOptimizationHistory } from '@/services/historyService';

export interface OptimizationHistory {
  id: string;
  original_text: string;
  optimized_text: string;
  platform: string;
  style: string;
  content_type: string;
  created_at: string;
  type: 'optimization';
}

export interface GenerationHistory {
  id: string;
  domain: string;
  topic: string | null;
  generated_content: string;
  created_at: string;
  type: 'generation';
}

export type HistoryItem = OptimizationHistory | GenerationHistory;

export const useHistory = () => {
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const { user } = useAuth();

  const fetchAllHistory = async () => {
    if (!user) {
      console.log('用户未登录，无法获取历史记录');
      return;
    }
    
    setIsLoadingHistory(true);
    try {
      console.log('开始获取历史记录，用户ID:', user.id);
      
      // 获取优化历史记录
      console.log('正在获取优化历史记录...');
      const { data: optimizationData, error: optimizationError } = await supabase
        .from('content_optimization_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (optimizationError) {
        console.error('获取优化历史记录失败:', optimizationError);
        throw optimizationError;
      }

      console.log('优化历史记录获取成功:', optimizationData?.length || 0, '条记录');

      // 获取生成历史记录
      console.log('正在获取生成历史记录...');
      const { data: generationData, error: generationError } = await supabase
        .from('content_generation_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (generationError) {
        console.error('获取生成历史记录失败:', generationError);
        throw generationError;
      }

      console.log('生成历史记录获取成功:', generationData?.length || 0, '条记录');

      // 合并并标记类型
      const optimizationItems: OptimizationHistory[] = (optimizationData || []).map(item => ({
        ...item,
        content_type: item.content_type || 'copywriting',
        type: 'optimization' as const
      }));

      const generationItems: GenerationHistory[] = (generationData || []).map(item => ({
        ...item,
        type: 'generation' as const
      }));

      // 按时间排序
      const allItems = [...optimizationItems, ...generationItems]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 30);

      console.log('所有历史记录合并完成:', {
        优化记录: optimizationItems.length,
        生成记录: generationItems.length,
        总计: allItems.length
      });
      
      setHistoryItems(allItems);
    } catch (error) {
      console.error('获取历史记录失败:', error);
      toast({
        title: "获取历史记录失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const saveOptimizationHistoryWrapper = async (
    originalText: string,
    optimizedText: string,
    platform: string,
    style: string,
    contentType: 'copywriting' | 'video-script' = 'copywriting'
  ) => {
    if (!user) {
      console.log('用户未登录，无法保存优化历史记录');
      return;
    }

    return await saveOptimizationHistory(user.id, originalText, optimizedText, platform, style, contentType);
  };

  const deleteHistoryItem = async (item: HistoryItem) => {
    try {
      const tableName = item.type === 'optimization' 
        ? 'content_optimization_history' 
        : 'content_generation_history';
      
      console.log('删除历史记录:', { id: item.id, type: item.type, table: tableName });
      
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', item.id);

      if (error) {
        console.error('删除历史记录失败:', error);
        throw error;
      }
      
      setHistoryItems(prev => prev.filter(historyItem => historyItem.id !== item.id));
      console.log('历史记录删除成功');
      toast({
        title: "删除成功",
        description: "历史记录已删除",
      });
    } catch (error) {
      console.error('删除历史记录失败:', error);
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    }
  };

  const copyContent = (content: string, type: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: `${type}内容已复制到剪贴板`,
    });
  };

  return {
    historyItems,
    isLoadingHistory,
    fetchAllHistory,
    saveOptimizationHistory: saveOptimizationHistoryWrapper,
    deleteHistoryItem,
    copyContent,
  };
};
