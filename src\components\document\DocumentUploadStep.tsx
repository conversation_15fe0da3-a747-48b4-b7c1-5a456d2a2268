
import React from 'react';
import { FileText } from 'lucide-react';
import { FileUploadCard } from './FileUploadCard';

interface DocumentUploadStepProps {
  file: File | null;
  isProcessing: boolean;
  isExtractingSummary: boolean;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onExtractSummary: () => void;
  onRemoveFile?: () => void;
}

export const DocumentUploadStep: React.FC<DocumentUploadStepProps> = ({
  file,
  isProcessing,
  isExtractingSummary,
  onFileChange,
  onExtractSummary,
  onRemoveFile
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
      <div className="space-y-6">
        <FileUploadCard
          file={file}
          isProcessing={isProcessing}
          onFileChange={onFileChange}
          onConvert={onExtractSummary}
          onRemoveFile={onRemoveFile}
          buttonText={isExtractingSummary ? "解析文档中..." : "解析文档"}
        />
      </div>

      <div className="bg-white/50 border border-gray-100 rounded-xl p-8 text-center flex flex-col justify-center min-h-[400px]">
        <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center mx-auto mb-4">
          <FileText className="w-8 h-8 text-gray-300" />
        </div>
        <h3 className="text-lg font-medium text-gray-500 mb-2">等待文档上传</h3>
        <p className="text-gray-400">上传文档后，AI将分析文档要点</p>
      </div>
    </div>
  );
};
