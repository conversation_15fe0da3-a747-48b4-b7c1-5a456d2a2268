
import React from 'react';
import { Sparkles } from 'lucide-react';
import OptimizationLoading from '@/components/OptimizationLoading';
import OptimizationResultNavigation from './OptimizationResultNavigation';

interface OptimizationResultProps {
  currentDisplayText: string;
  isOptimizing: boolean;
  isLoadingHistory: boolean;
  currentResultIndex: number;
  totalResults: number;
  currentResultCreatedAt?: string;
  onPreviousResult: () => void;
  onNextResult: () => void;
  onRegenerate: () => void;
  onCopyCurrentResult: () => void;
}

const OptimizationResult: React.FC<OptimizationResultProps> = ({
  currentDisplayText,
  isOptimizing,
  isLoadingHistory,
  currentResultIndex,
  totalResults,
  currentResultCreatedAt,
  onPreviousResult,
  onNextResult,
  onRegenerate,
  onCopyCurrentResult,
}) => {
  return (
    <div>
      <div className="flex items-center justify-between mb-3">
        <label className="block text-sm font-medium text-gray-700">
          优化结果
        </label>
        <OptimizationResultNavigation
          currentResultIndex={currentResultIndex}
          totalResults={totalResults}
          isOptimizing={isOptimizing}
          isLoadingHistory={isLoadingHistory}
          hasContent={!!currentDisplayText}
          onPreviousResult={onPreviousResult}
          onNextResult={onNextResult}
          onRegenerate={onRegenerate}
          onCopyCurrentResult={onCopyCurrentResult}
        />
      </div>
      
      {isOptimizing || isLoadingHistory ? (
        <OptimizationLoading />
      ) : currentDisplayText ? (
        <div className="bg-gradient-to-br from-gray-50/80 to-blue-50/30 border border-gray-200/70 rounded-2xl p-6 min-h-[320px] backdrop-blur-sm">
          <div className="whitespace-pre-wrap text-gray-800 leading-relaxed">
            {currentDisplayText}
          </div>
          {currentResultCreatedAt && (
            <div className="mt-4 pt-4 border-t border-gray-200/50">
              <div className="text-xs text-gray-500">
                生成时间: {new Date(currentResultCreatedAt).toLocaleString('zh-CN')}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-gradient-to-br from-gray-50/50 to-gray-100/30 border border-gray-200/50 rounded-2xl p-6 min-h-[320px] backdrop-blur-sm">
          <div className="text-gray-400 text-center py-24">
            <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-30" />
            <p className="text-lg">优化后的文案将在这里显示</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizationResult;
