import { appConfig } from '@/config/environment';
import { aiService as supabaseAIService } from './aiService';
import { volcanoService } from './volcanoService';

export interface AIService {
  hasValidApiKey(): boolean;
  setApiKey(apiKey: string): void;
  generateTopics(domain: string): Promise<string[]>;
  optimizeContent(originalText: string, platform?: string, style?: string): Promise<string>;
  generateVideoScript(originalText: string, platform?: string, style?: string): Promise<string>;
  generateContentByDomain(domain: string): Promise<string[]>;
  generateContentByTopic(topic: string): Promise<string[]>;
  generateContentByTopicStream(topic: string, onContent: (content: string) => void): Promise<string>;
  popularizeContent(content: string): Promise<string>;
  extractDocumentSummary(content: string): Promise<any>;
}

export const getAIService = (): AIService => {
  console.log(`Using AI service provider: ${appConfig.apiProvider}`);

  if (appConfig.apiProvider === 'volcano') {
    return volcanoService;
  } else {
    return supabaseAIService;
  }
};

// 默认AI服务实例
export const aiService = getAIService();