
interface UsageInstructionsProps {
  domainTitle: string;
}

const UsageInstructions = ({ domainTitle }: UsageInstructionsProps) => {
  return (
    <div className="text-center mt-12">
      <div className="bg-white/60 backdrop-blur rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">使用说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
            <span>内容已针对{domainTitle}领域优化</span>
          </div>
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
            <span>适合微信、微博等平台发布</span>
          </div>
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
            <span>包含专业标签便于传播</span>
          </div>
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
            <span>符合法律营销规范要求</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UsageInstructions;
