
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Co<PERSON>, Trash2 } from 'lucide-react';
import { OptimizationHistory, GenerationHistory, HistoryItem as HistoryItemType } from '@/hooks/useHistory';

interface HistoryItemProps {
  item: HistoryItemType;
  index: number;
  onLoad?: (item: OptimizationHistory) => void;
  onCopy: (content: string, type: string) => void;
  onDelete: (item: HistoryItemType) => void;
}

const HistoryItem: React.FC<HistoryItemProps> = ({ item, index, onLoad, onCopy, onDelete }) => {
  console.log(`渲染历史记录项 ${index}:`, item);

  return (
    <div className="border border-gray-200/60 rounded-2xl p-6 hover:bg-gray-50/50 transition-all duration-200 bg-white/60">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-3">
            {item.type === 'optimization' ? (
              <>
                <span className="text-xs bg-purple-100/80 text-purple-600 px-3 py-1.5 rounded-full font-medium">
                  内容优化
                </span>
                {(item as OptimizationHistory).platform && (
                  <span className="text-xs bg-blue-100/80 text-blue-600 px-3 py-1.5 rounded-full font-medium">
                    {(item as OptimizationHistory).platform}
                  </span>
                )}
                {(item as OptimizationHistory).style && (
                  <span className="text-xs bg-green-100/80 text-green-600 px-3 py-1.5 rounded-full font-medium">
                    {(item as OptimizationHistory).style}
                  </span>
                )}
              </>
            ) : (
              <>
                <span className="text-xs bg-orange-100/80 text-orange-600 px-3 py-1.5 rounded-full font-medium">
                  内容生成
                </span>
                <span className="text-xs bg-blue-100/80 text-blue-600 px-3 py-1.5 rounded-full font-medium">
                  {(item as GenerationHistory).domain}
                </span>
                {(item as GenerationHistory).topic && (
                  <span className="text-xs bg-green-100/80 text-green-600 px-3 py-1.5 rounded-full font-medium">
                    {(item as GenerationHistory).topic}
                  </span>
                )}
              </>
            )}
            <span className="text-xs text-gray-500 bg-gray-100/80 px-3 py-1.5 rounded-full">
              {new Date(item.created_at).toLocaleString('zh-CN')}
            </span>
          </div>
          
          {item.type === 'optimization' ? (
            <>
              <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                <strong>原文:</strong> {(item as OptimizationHistory).original_text}
              </p>
              <p className="text-sm text-gray-600 line-clamp-2">
                <strong>优化后:</strong> {(item as OptimizationHistory).optimized_text}
              </p>
            </>
          ) : (
            <p className="text-sm text-gray-700 line-clamp-3">
              <strong>生成内容:</strong> {(item as GenerationHistory).generated_content}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3 ml-6">
          {item.type === 'optimization' ? (
            <Button
              onClick={() => onLoad?.(item as OptimizationHistory)}
              variant="outline"
              size="sm"
              className="rounded-xl bg-blue-50/80 border-blue-200/70 text-blue-600 hover:bg-blue-100/80 transition-all duration-200"
            >
              使用
            </Button>
          ) : (
            <Button
              onClick={() => onCopy((item as GenerationHistory).generated_content, '生成')}
              variant="outline"
              size="sm"
              className="rounded-xl bg-blue-50/80 border-blue-200/70 text-blue-600 hover:bg-blue-100/80 transition-all duration-200"
            >
              <Copy className="w-3 h-3 mr-1" />
              复制
            </Button>
          )}
          <Button
            onClick={() => onDelete(item)}
            variant="outline"
            size="sm"
            className="rounded-xl text-red-600 hover:text-red-700 border-red-200/70 hover:bg-red-50/80 transition-all duration-200"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HistoryItem;
