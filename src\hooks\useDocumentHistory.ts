
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

export interface DocumentHistoryItem {
  id: string;
  original_filename: string;
  original_content: string;
  popularized_content: string;
  created_at: string;
}

export const useDocumentHistory = () => {
  const [historyItems, setHistoryItems] = useState<DocumentHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchHistory = async () => {
    if (!user) {
      console.log('用户未登录，无法获取历史记录');
      return;
    }
    
    setIsLoading(true);
    try {
      console.log('开始获取文档转换历史记录，用户ID:', user.id);
      
      const { data, error } = await supabase
        .from('document_conversion_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('获取文档转换历史记录失败:', error);
        throw error;
      }

      console.log('文档转换历史记录获取成功:', data?.length || 0, '条记录');
      setHistoryItems(data || []);
    } catch (error) {
      console.error('获取历史记录失败:', error);
      toast({
        title: "获取历史记录失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteHistoryItem = async (itemId: string) => {
    try {
      console.log('删除文档转换历史记录:', itemId);
      
      const { error } = await supabase
        .from('document_conversion_history')
        .delete()
        .eq('id', itemId);

      if (error) {
        console.error('删除历史记录失败:', error);
        throw error;
      }
      
      setHistoryItems(prev => prev.filter(item => item.id !== itemId));
      console.log('历史记录删除成功');
      toast({
        title: "删除成功",
        description: "历史记录已删除",
      });
    } catch (error) {
      console.error('删除历史记录失败:', error);
      toast({
        title: "删除失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    }
  };

  const copyContent = (content: string, type: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: "复制成功",
      description: `${type}内容已复制到剪贴板`,
    });
  };

  useEffect(() => {
    if (user) {
      fetchHistory();
    }
  }, [user]);

  return {
    historyItems,
    isLoading,
    fetchHistory,
    deleteHistoryItem,
    copyContent,
  };
};
