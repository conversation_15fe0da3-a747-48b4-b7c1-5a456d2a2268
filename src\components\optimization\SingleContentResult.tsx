
import React from 'react';
import { FileText, Video, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EditableResult from './EditableResult';
import VersionNavigation from './VersionNavigation';

interface SingleContentResultProps {
  contentType: string;
  content: string;
  isGenerating: boolean;
  currentIndex: number;
  totalCount: number;
  onPrevious: () => void;
  onNext: () => void;
  onContentChange: (content: string) => void;
  onCopy: () => void;
}

const SingleContentResult: React.FC<SingleContentResultProps> = ({
  contentType,
  content,
  isGenerating,
  currentIndex,
  totalCount,
  onPrevious,
  onNext,
  onContentChange,
  onCopy
}) => {
  return (
    <div className="bg-gradient-to-br from-gray-50/80 to-sky-50/30 border border-gray-200/70 rounded-2xl p-6 min-h-[320px] backdrop-blur-sm">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {/* Use similar styling to active tab for consistency */}
          <div className="inline-flex h-12 items-center justify-center rounded-xl bg-white/80 backdrop-blur-sm p-1 border border-gray-200/60 shadow-sm">
            <div className="inline-flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-200/50 rounded-lg">
              {contentType === 'copywriting' ? (
                <FileText className="w-4 h-4" />
              ) : (
                <Video className="w-4 h-4" />
              )}
              <span className="text-sm font-medium whitespace-nowrap">
                {contentType === 'copywriting' ? '文案' : '短视频脚本'}
              </span>
            </div>
          </div>

          <VersionNavigation
            currentIndex={currentIndex}
            totalCount={totalCount}
            onPrevious={onPrevious}
            onNext={onNext}
          />
        </div>
        
        {content && !isGenerating && (
          <Button
            onClick={onCopy}
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-gray-100"
            title="复制内容"
          >
            <Copy className="w-4 h-4" />
          </Button>
        )}
      </div>
      
      <div className="border border-gray-200/80 rounded-xl p-4 bg-white/50">
        <EditableResult
          content={content}
          isGenerating={isGenerating}
          contentType={contentType as 'copywriting' | 'video-script'}
          onContentChange={onContentChange}
          onCopy={onCopy}
          showCopyButton={false}
        />
      </div>
    </div>
  );
};

export default SingleContentResult;
