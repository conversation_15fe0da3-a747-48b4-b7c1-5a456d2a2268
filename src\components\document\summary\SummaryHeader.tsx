
import React from 'react';
import { FileText } from 'lucide-react';
import { CardHeader, CardTitle } from '@/components/ui/card';

export const SummaryHeader: React.FC = () => {
  return (
    <CardHeader className="bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 border-b border-blue-100/50">
      <CardTitle className="flex items-center space-x-4">
        <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
          <FileText className="w-6 h-6 text-white" />
        </div>
        <div>
          <span className="text-2xl font-bold bg-gradient-to-r from-blue-700 to-purple-700 bg-clip-text text-transparent">
            争议焦点与裁判要点
          </span>
          <p className="text-sm text-gray-600 font-normal mt-1">
            请选择您希望在科普文案中重点呈现的争议焦点部分
          </p>
        </div>
      </CardTitle>
    </CardHeader>
  );
};
