const fetch = require('node-fetch');
const FormData = require('form-data');

// CORS头设置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// 从detail数组重建markdown内容的函数
function rebuildContentFromDetail(result) {
  if (!result?.detail || !Array.isArray(result.detail)) {
    console.log('No detail array found in result');
    return '';
  }

  console.log(`Rebuilding content from ${result.detail.length} detail items`);
  
  // 按page_id和paragraph_id排序
  const sortedDetails = result.detail.sort((a, b) => {
    if (a.page_id !== b.page_id) {
      return (a.page_id || 0) - (b.page_id || 0);
    }
    return (a.paragraph_id || 0) - (b.paragraph_id || 0);
  });
  
  const markdownLines = [];
  let lastOutlineLevel = -1;
  
  for (const item of sortedDetails) {
    if (!item.text || typeof item.text !== 'string') {
      continue;
    }
    
    const text = item.text.trim();
    if (!text) continue;
    
    const outlineLevel = item.outline_level || 0;
    
    // 根据outline_level添加markdown标题格式
    if (outlineLevel > 0 && outlineLevel <= 6) {
      const headerPrefix = '#'.repeat(outlineLevel);
      markdownLines.push(`${headerPrefix} ${text}`);
    } else if (outlineLevel > lastOutlineLevel && lastOutlineLevel >= 0) {
      // 如果outline_level增加，可能是子标题
      const headerLevel = Math.min(lastOutlineLevel + 1, 6);
      const headerPrefix = '#'.repeat(headerLevel);
      markdownLines.push(`${headerPrefix} ${text}`);
    } else {
      // 普通段落
      markdownLines.push(text);
    }
    
    markdownLines.push(''); // 添加空行分隔
    
    lastOutlineLevel = outlineLevel;
  }
  
  const rebuiltMarkdown = markdownLines.join('\n').trim();
  console.log(`Rebuilt markdown from detail array, length: ${rebuiltMarkdown.length}`);
  
  return rebuiltMarkdown;
}

// 带重试机制的API调用函数
async function callTextinWithRetry(fileBuffer, textinAppId, textinSecretCode, fileName, retryCount = 0) {
  try {
    const response = await fetch('https://api.textin.com/ai/service/v1/pdf_to_markdown', {
      method: 'POST',
      headers: {
        'x-ti-app-id': textinAppId,
        'x-ti-secret-code': textinSecretCode,
        'Content-Type': 'application/octet-stream',
      },
      body: fileBuffer
    });
    
    console.log(`Textin API call attempt ${retryCount + 1}, status:`, response.status);
    return response;
  } catch (error) {
    console.error(`Textin API call attempt ${retryCount + 1} failed:`, error);
    if (retryCount < 2) { // 最多重试2次
      console.log(`Retrying in ${(retryCount + 1) * 1000}ms...`);
      await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
      return callTextinWithRetry(fileBuffer, textinAppId, textinSecretCode, fileName, retryCount + 1);
    }
    throw error;
  }
}

exports.handler = async (event, context) => {
  // 处理CORS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // 验证请求方法
    if (event.httpMethod !== 'POST') {
      throw new Error('Only POST method is allowed');
    }

    // 获取环境变量
    const textinAppId = process.env.TEXTIN_APP_ID;
    const textinSecretCode = process.env.TEXTIN_SECRET_CODE;

    console.log('Textin API credentials check:', { 
      hasAppId: !!textinAppId, 
      hasSecretCode: !!textinSecretCode,
      appIdLength: textinAppId?.length || 0
    });

    if (!textinAppId || !textinSecretCode) {
      console.error('Missing Textin API credentials:', { textinAppId: !!textinAppId, textinSecretCode: !!textinSecretCode });
      throw new Error('Textin API credentials not configured');
    }

    // 解析multipart/form-data
    const contentType = event.headers['content-type'] || event.headers['Content-Type'];
    if (!contentType || !contentType.includes('multipart/form-data')) {
      throw new Error('Content-Type must be multipart/form-data');
    }

    // 从base64解码文件内容
    const body = event.isBase64Encoded ? Buffer.from(event.body, 'base64') : Buffer.from(event.body);
    
    // 简单的multipart解析（实际项目中建议使用专门的库）
    const boundary = contentType.split('boundary=')[1];
    const parts = body.toString('binary').split(`--${boundary}`);
    
    let fileBuffer = null;
    let fileName = 'document';
    
    for (const part of parts) {
      if (part.includes('Content-Disposition: form-data; name="file"')) {
        const lines = part.split('\r\n');
        const contentDisposition = lines.find(line => line.includes('Content-Disposition'));
        if (contentDisposition && contentDisposition.includes('filename=')) {
          fileName = contentDisposition.split('filename=')[1].replace(/"/g, '').trim();
        }
        
        // 找到文件内容开始位置
        const emptyLineIndex = lines.findIndex(line => line === '');
        if (emptyLineIndex !== -1) {
          const fileContent = lines.slice(emptyLineIndex + 1).join('\r\n');
          fileBuffer = Buffer.from(fileContent, 'binary');
          break;
        }
      }
    }

    if (!fileBuffer) {
      throw new Error('No file provided');
    }

    console.log('Processing file:', {
      name: fileName,
      size: fileBuffer.length,
      sizeMB: (fileBuffer.length / 1024 / 1024).toFixed(2)
    });

    // 检查文件大小限制（500MB）
    if (fileBuffer.length > 500 * 1024 * 1024) {
      throw new Error('文件大小超过500MB限制，请选择较小的文件');
    }

    // 验证文件格式
    const validExtensions = ['.pdf', '.doc', '.docx'];
    const validMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    const hasValidExtension = validExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
    
    if (!hasValidExtension) {
      throw new Error('文件格式不支持，仅支持PDF、DOC、DOCX格式');
    }

    // 调用Textin API
    const textinResponse = await callTextinWithRetry(fileBuffer, textinAppId, textinSecretCode, fileName);

    console.log('Textin API response status:', textinResponse.status);

    if (!textinResponse.ok) {
      const errorText = await textinResponse.text();
      console.error('Textin API HTTP error:', textinResponse.status, errorText);
      
      // 根据HTTP状态码提供更具体的错误信息
      if (textinResponse.status === 413) {
        throw new Error('文件过大，请选择较小的文件（最大500MB）');
      } else if (textinResponse.status === 415) {
        const isWordDoc = fileName.endsWith('.doc') || fileName.endsWith('.docx');
        if (isWordDoc) {
          throw new Error('Word文档格式不兼容，请尝试：\n1. 将文档另存为较新的.docx格式\n2. 转换为PDF格式后重新上传\n3. 检查文档是否有密码保护');
        } else {
          throw new Error('文件格式不支持，请尝试转换为PDF格式后重新上传');
        }
      } else if (textinResponse.status >= 500) {
        throw new Error('文档解析服务暂时不可用，请稍后重试');
      } else {
        throw new Error(`文档解析失败，错误代码：${textinResponse.status}`);
      }
    }

    const result = await textinResponse.json();
    console.log('Textin API response code:', result.code);
    console.log('Textin API response details:', {
      total_pages: result.result?.total_page_number,
      valid_pages: result.result?.valid_page_number,
      document_type: result.result?.document_type,
      markdown_length: result.result?.markdown?.length || 0,
      detail_items: result.result?.detail?.length || 0
    });

    // 处理Textin API特定的错误码
    if (result.code !== 200) {
      let errorMessage = '文档解析失败';
      
      switch (result.code) {
        case 40425:
          errorMessage = '文档格式不兼容，请尝试以下解决方案：\n1. 将文档另存为较新的格式（如.docx或PDF）\n2. 检查文档是否有密码保护\n3. 尝试使用其他文档编辑器重新保存';
          break;
        case 40401:
          errorMessage = 'API认证失败，请联系管理员';
          break;
        case 40429:
          errorMessage = '请求过于频繁，请稍后重试';
          break;
        case 50001:
        case 50002:
          errorMessage = '文档解析服务暂时不可用，请稍后重试';
          break;
        default:
          errorMessage = `文档解析失败：${result.message || '未知错误'}`;
      }
      
      throw new Error(errorMessage);
    }

    // 增强的内容提取逻辑
    let markdown = result.result?.markdown || '';
    
    // 如果直接的markdown内容不足，尝试从detail数组重建
    if (!markdown || markdown.trim().length < 100) {
      console.log('Direct markdown insufficient, attempting to rebuild from detail array');
      markdown = rebuildContentFromDetail(result.result);
    }
    
    if (!markdown || markdown.trim().length < 100) {
      const isWordDoc = fileName.endsWith('.doc') || fileName.endsWith('.docx');
      if (isWordDoc) {
        throw new Error('Word文档内容提取失败，可能原因：\n1. 文档格式过于复杂或包含大量图像\n2. 文档受到密码保护\n3. 文档格式损坏\n请尝试将文档转换为PDF格式后重新上传');
      } else {
        throw new Error('文档内容提取失败，可能是文档为空或格式损坏');
      }
    }

    console.log('Successfully extracted markdown, length:', markdown.length);

    return {
      statusCode: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      body: JSON.stringify({ markdown })
    };

  } catch (error) {
    console.error('Error in textin-convert function:', error);
    return {
      statusCode: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: error.message })
    };
  }
};
