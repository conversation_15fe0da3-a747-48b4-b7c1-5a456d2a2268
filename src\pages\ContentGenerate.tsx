
import { useNavigate } from "react-router-dom";
import Header from "@/components/layout/Header";
import GenerationTab from "@/components/generation/GenerationTab";

const ContentGenerate = () => {
  const navigate = useNavigate();

  const handleGenerateContent = () => {
    navigate("/domains");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Header />

      <div className="max-w-6xl mx-auto px-6 md:px-6 py-8">
        <GenerationTab onGenerateContent={handleGenerateContent} />
      </div>
    </div>
  );
};

export default ContentGenerate;
