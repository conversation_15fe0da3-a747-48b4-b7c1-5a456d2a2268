/**
 * 并发处理测试工具
 * 用于验证新的并发生成逻辑是否正常工作
 */

// 模拟API调用的延迟和成功率
const simulateApiCall = async (topic: string, index: number): Promise<string> => {
  // 模拟不同的延迟时间 (1-5秒)
  const delay = Math.random() * 4000 + 1000;
  
  // 模拟90%的成功率
  const shouldSucceed = Math.random() > 0.1;
  
  await new Promise(resolve => setTimeout(resolve, delay));
  
  if (!shouldSucceed) {
    throw new Error(`模拟API失败 - 选题${index + 1}`);
  }
  
  return `这是为选题"${topic}"生成的模拟内容。内容包含了专业的法律建议和实用的解决方案。`;
};

// 测试并发处理性能
export const testConcurrentProcessing = async (topics: string[]) => {
  console.log('🧪 开始并发处理测试');
  console.log(`📊 测试参数: ${topics.length}个选题，并发数量: 3`);
  
  const startTime = Date.now();
  const batchSize = 3;
  const results: Array<{success: boolean, topic: string, duration: number}> = [];
  
  try {
    // 分批处理
    for (let i = 0; i < topics.length; i += batchSize) {
      const batch = topics.slice(i, i + batchSize);
      console.log(`🔄 处理批次 ${Math.floor(i / batchSize) + 1}: 选题 ${i + 1}-${Math.min(i + batchSize, topics.length)}`);
      
      const batchStartTime = Date.now();
      const batchPromises = batch.map((topic, batchIndex) => 
        simulateApiCall(topic, i + batchIndex)
          .then(content => ({
            success: true,
            topic,
            content,
            duration: Date.now() - batchStartTime
          }))
          .catch(error => ({
            success: false,
            topic,
            error: error.message,
            duration: Date.now() - batchStartTime
          }))
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, batchIndex) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
          const status = result.value.success ? '✅' : '❌';
          console.log(`${status} 选题 ${i + batchIndex + 1}: ${result.value.topic}`);
        }
      });
    }
    
    const totalTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    console.log('\n📈 测试结果统计:');
    console.log(`⏱️  总耗时: ${(totalTime / 1000).toFixed(2)}秒`);
    console.log(`✅ 成功: ${successCount}个`);
    console.log(`❌ 失败: ${failureCount}个`);
    console.log(`📊 成功率: ${Math.round((successCount / topics.length) * 100)}%`);
    
    if (failureCount > 0) {
      const failedTopics = results.filter(r => !r.success).map(r => r.topic);
      console.log(`🔍 失败的选题:`, failedTopics);
    }
    
    return {
      totalTime,
      successCount,
      failureCount,
      successRate: successCount / topics.length,
      results
    };
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
};

// 对比顺序处理和并发处理的性能
export const compareProcessingMethods = async (topics: string[]) => {
  console.log('🔬 开始性能对比测试\n');
  
  // 测试顺序处理
  console.log('1️⃣ 测试顺序处理:');
  const sequentialStart = Date.now();
  let sequentialSuccess = 0;
  
  for (let i = 0; i < topics.length; i++) {
    try {
      await simulateApiCall(topics[i], i);
      sequentialSuccess++;
      console.log(`✅ 顺序处理 - 选题 ${i + 1} 完成`);
    } catch (error) {
      console.log(`❌ 顺序处理 - 选题 ${i + 1} 失败`);
    }
  }
  
  const sequentialTime = Date.now() - sequentialStart;
  
  console.log(`\n📊 顺序处理结果:`);
  console.log(`⏱️  耗时: ${(sequentialTime / 1000).toFixed(2)}秒`);
  console.log(`✅ 成功: ${sequentialSuccess}/${topics.length}`);
  
  // 测试并发处理
  console.log('\n2️⃣ 测试并发处理:');
  const concurrentResult = await testConcurrentProcessing(topics);
  
  // 性能对比
  console.log('\n🏆 性能对比结果:');
  console.log(`⚡ 速度提升: ${(sequentialTime / concurrentResult.totalTime).toFixed(2)}x`);
  console.log(`📈 时间节省: ${((sequentialTime - concurrentResult.totalTime) / 1000).toFixed(2)}秒`);
  
  return {
    sequential: {
      time: sequentialTime,
      success: sequentialSuccess
    },
    concurrent: concurrentResult,
    speedup: sequentialTime / concurrentResult.totalTime
  };
};

// 生成测试用的选题数据
export const generateTestTopics = (count: number = 10): string[] => {
  const sampleTopics = [
    "房产纠纷中的热门选题",
    "婚姻财产分割的法律要点",
    "劳动合同纠纷处理指南",
    "消费者权益保护实务",
    "公司法务常见问题",
    "债务纠纷解决方案",
    "刑事辩护策略分析",
    "合同审查要点梳理",
    "知识产权保护措施",
    "民事诉讼程序指导"
  ];
  
  return sampleTopics.slice(0, count);
};
