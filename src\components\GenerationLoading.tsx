
import { Sparkles } from "lucide-react";

interface GenerationLoadingProps {
  domainTitle: string;
}

const GenerationLoading = ({ domainTitle }: GenerationLoadingProps) => {
  return (
    <div className="text-center py-20">
      <div className="bg-white rounded-lg p-8 shadow-lg max-w-md mx-auto">
        <Sparkles className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-pulse" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">AI正在生成内容</h3>
        <p className="text-gray-600 mb-4">正在为您生成专业的{domainTitle}文案...</p>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '70%' }}></div>
        </div>
      </div>
    </div>
  );
};

export default GenerationLoading;
