
import React from 'react';
import { DocumentSummarySelector } from './DocumentSummarySelector';
import { ContentDisplayCard } from './ContentDisplayCard';

interface DocumentSummaryStepProps {
  documentSummary: any;
  selectedSections: string[];
  originalContent: string;
  isGeneratingPopularized: boolean;
  onSectionSelection: (sections: string[]) => void;
  onContinue: () => void;
  onRestart: () => void;
}

export const DocumentSummaryStep: React.FC<DocumentSummaryStepProps> = ({
  documentSummary,
  selectedSections,
  originalContent,
  isGeneratingPopularized,
  onSectionSelection,
  onContinue,
  onRestart
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div className="space-y-6">
        <DocumentSummarySelector
          summary={documentSummary}
          selectedSections={selectedSections}
          onSectionSelection={onSectionSelection}
          onContinue={onContinue}
          isGenerating={isGeneratingPopularized}
        />
      </div>

      <div className="space-y-6">
        <ContentDisplayCard
          title="原始内容"
          content={originalContent}
          filename="原始内容.md"
        />
      </div>
    </div>
  );
};
