import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { DoubaoService } from './doubaoService.ts'
import { cleanXiaohongshuContent } from './textProcessing.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // 验证用户身份
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      throw new Error('Invalid user')
    }

    const { action, data } = await req.json()

    console.log(`Processing request: ${action} for user: ${user.id} using Doubao model`)

    let result
    const doubaoService = new DoubaoService()

    switch (action) {
      case 'generateTopics':
        result = await doubaoService.generateTopics(data.domain)
        break
      case 'optimize':
        result = await doubaoService.optimizeContent(data.text, data.platform, data.style)
        break
      case 'generateVideoScript':
        result = await doubaoService.generateVideoScript(data.text, data.platform, data.style)
        break
      case 'generateContent':
        result = await doubaoService.generateContentByDomain(data.domain)
        break
      case 'generateContentByTopic':
        result = await doubaoService.generateContentByTopic(data.topic)
        break
      case 'generateContentByTopicStream':
        // 对于流式生成，暂时使用非流式方式返回结果
        const contents = await doubaoService.generateContentByTopic(data.topic)
        // 返回第一条内容作为单条文案，并进行清理
        const rawContent = contents.length > 0 ? contents[0] : ''
        result = cleanXiaohongshuContent(rawContent)
        break
      case 'popularize':
        result = await doubaoService.popularizeContent(data.content)
        break
      case 'extractDocumentSummary':
        result = await doubaoService.extractDocumentSummary(data.content)
        break
      default:
        throw new Error('Invalid action')
    }

    return new Response(
      JSON.stringify({ result }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in doubao-ai function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
