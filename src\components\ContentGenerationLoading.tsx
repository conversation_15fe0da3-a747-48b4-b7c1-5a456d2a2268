
import { useEffect, useState } from 'react';
import { Sparkles, FileText, Wand2, Zap, Target } from 'lucide-react';

interface ContentGenerationLoadingProps {
  title?: string;
  description?: string;
  selectedTopics?: string[];
}

const contentLoadingItems = [
  {
    text: "正在分析选题深度",
    icon: Target,
  },
  {
    text: "正在构思内容框架", 
    icon: FileText,
  },
  {
    text: "正在优化表达方式",
    icon: Wand2,
  },
  {
    text: "正在注入创意灵感",
    icon: Zap,
  },
  {
    text: "正在润色文案细节",
    icon: Sparkles,
  },
];

const ContentGenerationLoading = ({ 
  title = "正在生成专业文案", 
  description = "AI正在为您精心创作...",
  selectedTopics = []
}: ContentGenerationLoadingProps) => {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);

  useEffect(() => {
    // 逐行延迟显示动画
    contentLoadingItems.forEach((_, index) => {
      setTimeout(() => {
        setVisibleItems(prev => [...prev, index]);
      }, index * 300); // 每项延迟300ms
    });

    return () => {
      setVisibleItems([]);
    };
  }, []);

  return (
    <div className="text-center py-20">
      <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-xl max-w-2xl mx-auto border border-white/50">
        {/* 标题区域 */}
        <div className="mb-8">
          <div className="relative">
            <Sparkles className="w-16 h-16 text-purple-600 mx-auto mb-4 loading-float" />
            <div className="absolute inset-0 w-16 h-16 mx-auto mb-4">
              <div className="w-full h-full border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin"></div>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-4">{description}</p>
          
          {selectedTopics.length > 0 && (
            <div className="bg-purple-50/50 rounded-2xl p-4 mb-4">
              <p className="text-sm text-purple-700 font-medium mb-2">正在处理的选题：</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {selectedTopics.map((topic, index) => (
                  <span 
                    key={index}
                    className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs border border-purple-200"
                  >
                    {topic.length > 15 ? `${topic.substring(0, 15)}...` : topic}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 动画列表 */}
        <div className="space-y-4">
          {contentLoadingItems.map((item, index) => {
            const Icon = item.icon;
            const isVisible = visibleItems.includes(index);
            
            return (
              <div
                key={index}
                className={`
                  loading-item flex items-center justify-center space-x-3 p-4 rounded-2xl 
                  transition-all duration-600 ease-out cursor-default
                  ${isVisible 
                    ? 'opacity-100 translate-y-0' 
                    : 'opacity-0 translate-y-6'
                  }
                  hover:bg-purple-50/60 hover:text-purple-700 hover:scale-105
                  bg-gray-50/30 border border-gray-200/30
                `}
                style={{
                  transitionDelay: isVisible ? '0ms' : `${index * 150}ms`
                }}
              >
                <Icon className="w-5 h-5 text-purple-500 transition-all duration-300 hover:text-purple-700 hover:rotate-12" />
                <span className="text-gray-700 font-medium transition-colors duration-300 hover:text-purple-800">
                  {item.text}
                </span>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 进度指示器 */}
        <div className="mt-8">
          <div className="w-full bg-gray-200/50 rounded-full h-3 overflow-hidden">
            <div 
              className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 h-3 rounded-full transition-all duration-1000 ease-out loading-progress"
              style={{ 
                width: `${(visibleItems.length / contentLoadingItems.length) * 100}%`,
              }}
            ></div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            创作进度: {Math.round((visibleItems.length / contentLoadingItems.length) * 100)}%
          </p>
        </div>
      </div>
    </div>
  );
};

export default ContentGenerationLoading;
