
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Scale, Sparkles, FileText, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import UserProfile from '@/components/UserProfile';
const Header: React.FC = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  if (isMobile) {
    return <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
        <div className="px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3" onClick={() => navigate('/')} style={{
            cursor: 'pointer'
          }}>
              <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-2.5 rounded-xl shadow-md">
                <Scale className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">法律文案助手</h1>
                <p className="text-xs text-gray-600">AI驱动的专业文案工具</p>
              </div>
            </div>
            <UserProfile />
          </div>
          
          {/* 移动端导航栏 */}
          <div className="flex items-center justify-center mt-4">
            <div className="flex items-center bg-gray-50/80 backdrop-blur-sm rounded-xl p-1 w-full">
              <Button variant="ghost" onClick={() => navigate('/optimize')} className="flex-1 flex items-center justify-center space-x-1 px-2 py-2 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600 text-xs">
                <Sparkles className="w-3 h-3" />
                <span className="font-medium">内容优化</span>
              </Button>
              
              <Button variant="ghost" onClick={() => navigate('/content-generate')} className="flex-1 flex items-center justify-center space-x-1 px-2 py-2 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600 text-xs">
                <FileText className="w-3 h-3" />
                <span className="font-medium">内容生成</span>
              </Button>

              <Button variant="ghost" onClick={() => navigate('/document-convert')} className="flex-1 flex items-center justify-center space-x-1 px-2 py-2 rounded-lg hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600 text-xs">
                <BookOpen className="w-3 h-3" />
                <span className="font-medium">判决书科普</span>
              </Button>
            </div>
          </div>
        </div>
      </header>;
  }

  // PC端设计
  return <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm">
      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4" onClick={() => navigate('/')} style={{
          cursor: 'pointer'
        }}>
            <div className="bg-gradient-to-br from-blue-500 to-blue-700 p-3 rounded-2xl shadow-md">
              <Scale className="h-7 w-7 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">法律文案助手</h1>
              <p className="text-sm text-gray-600 mt-1">AI驱动的专业文案工具</p>
            </div>
          </div>

          {/* 居中的导航栏 - 去掉外框线 */}
          <div className="flex-1 flex justify-center">
            <div className="flex items-center bg-gray-50/80 backdrop-blur-sm rounded-2xl p-1">
              <Button variant="ghost" onClick={() => navigate('/optimize')} className="flex items-center space-x-2 px-4 py-2 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600">
                <Sparkles className="w-4 h-4" />
                <span className="font-medium">内容优化</span>
              </Button>
              
              <Button variant="ghost" onClick={() => navigate('/content-generate')} className="flex items-center space-x-2 px-4 py-2 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600">
                <FileText className="w-4 h-4" />
                <span className="font-medium">内容生成</span>
              </Button>

              <Button variant="ghost" onClick={() => navigate('/document-convert')} className="flex items-center space-x-2 px-4 py-2 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 text-gray-700 hover:text-blue-600">
                <BookOpen className="w-4 h-4" />
                <span className="font-medium">判决书科普</span>
              </Button>
            </div>
          </div>

          <div className="flex items-center">
            <UserProfile />
          </div>
        </div>
      </div>
    </header>;
};
export default Header;
