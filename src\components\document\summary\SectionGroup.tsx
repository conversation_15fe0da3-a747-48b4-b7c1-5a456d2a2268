
import React from 'react';
import { SectionCard } from './SectionCard';

interface Section {
  key: string;
  title: string;
  description: string;
  content: string;
  icon: string;
  type: string;
}

interface SectionGroupProps {
  title: string;
  sections: Section[];
  icon: React.ReactNode;
  selectedSections: string[];
  onSectionToggle: (sectionKey: string) => void;
}

export const SectionGroup: React.FC<SectionGroupProps> = ({
  title,
  sections,
  icon,
  selectedSections,
  onSectionToggle
}) => {
  if (sections.length === 0) return null;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 px-2">
        {icon}
        <h4 className="font-semibold text-gray-800 text-lg">{title}</h4>
        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {sections.length} 项
        </span>
      </div>
      
      <div className="space-y-4">
        {sections.map((section) => (
          <SectionCard
            key={section.key}
            section={section}
            isSelected={selectedSections.includes(section.key)}
            onToggle={onSectionToggle}
          />
        ))}
      </div>
    </div>
  );
};
