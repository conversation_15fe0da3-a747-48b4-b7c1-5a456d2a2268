
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import AuthGuard from "@/components/AuthGuard";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Optimize from "./pages/Optimize";
import ContentGenerate from "./pages/ContentGenerate";
import Domains from "./pages/Domains";
import Topics from "./pages/Topics";
import Generate from "./pages/Generate";
import Landing from "./pages/Landing";
import Demo from "./pages/Demo";
import DocumentConvert from "./pages/DocumentConvert";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Landing />} />
            <Route path="/demo" element={<Demo />} />
            <Route path="/auth" element={<Auth />} />
            <Route path="/app" element={<AuthGuard><Index /></AuthGuard>} />
            <Route path="/optimize" element={<AuthGuard><Optimize /></AuthGuard>} />
            <Route path="/content-generate" element={<AuthGuard><ContentGenerate /></AuthGuard>} />
            <Route path="/domains" element={<Domains />} />
            <Route path="/topics/:domain" element={<Topics />} />
            <Route path="/generate/:domain" element={<AuthGuard><Generate /></AuthGuard>} />
            <Route path="/document-convert" element={<AuthGuard><DocumentConvert /></AuthGuard>} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
