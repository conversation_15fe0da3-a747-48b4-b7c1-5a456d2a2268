const fetch = require('node-fetch');

// CORS头设置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// 豆包API服务类
class DoubaoService {
  constructor() {
    this.apiKey = process.env.DOUBAO_API_KEY;
    this.baseUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
    this.model = 'doubao-seed-1-6-thinking-250615';
    
    if (!this.apiKey) {
      throw new Error('DOUBAO_API_KEY environment variable is required');
    }
  }

  async callDoubaoAPI(input) {
    try {
      const requestBody = {
        model: this.model,
        messages: [
          {
            role: "user",
            content: input
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      };
      
      console.log('Calling Doubao Chat API with body:', JSON.stringify(requestBody, null, 2));
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Doubao API response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Doubao API error:', response.status, errorText);
        throw new Error(`Doubao API error: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      console.log('Doubao API response:', JSON.stringify(result, null, 2));
      
      // 从Chat API的响应中提取文本内容
      let content = '';
      if (result.choices && Array.isArray(result.choices) && result.choices.length > 0) {
        const choice = result.choices[0];
        if (choice.message && choice.message.content) {
          content = choice.message.content;
        }
      }
      
      console.log('Extracted content:', content);
      return content || '';
    } catch (error) {
      console.error('Error calling Doubao API:', error);
      throw new Error(`Failed to call Doubao API: ${error.message}`);
    }
  }

  // 生成选题
  async generateTopics(domain) {
    const prompt = `请为"${domain}"领域生成10个热门选题，要求：
1. 选题要具有时效性和话题性
2. 适合制作成短视频或图文内容
3. 能够吸引目标受众关注
4. 每个选题用一行表示，格式为"- 选题标题"

请直接输出选题列表，不要其他说明文字。`;

    const response = await this.callDoubaoAPI(prompt);
    
    // 解析选题
    const topics = response
      .split('\n')
      .filter(line => line.trim().startsWith('-'))
      .map(line => line.replace(/^-\s*/, '').trim())
      .filter(topic => topic.length > 0);
    
    return topics;
  }

  // 优化内容
  async optimizeContent(text, platform, style) {
    const prompt = `请优化以下内容，使其更适合${platform}平台，风格要求：${style}

原始内容：
${text}

优化要求：
1. 保持核心信息不变
2. 调整语言风格和表达方式
3. 优化结构和排版
4. 增强可读性和吸引力

请直接输出优化后的内容，不要其他说明文字。`;

    return await this.callDoubaoAPI(prompt);
  }

  // 生成视频脚本
  async generateVideoScript(text, platform, style) {
    const prompt = `基于以下内容，为${platform}平台生成一个${style}风格的短视频脚本：

原始内容：
${text}

脚本要求：
1. 时长控制在60-90秒
2. 包含开头、主体、结尾三部分
3. 语言生动有趣，适合口播
4. 包含适当的停顿和语调提示
5. 结尾要有明确的行动号召

请按以下格式输出：
【开头】(0-10秒)
...

【主体】(10-70秒)
...

【结尾】(70-90秒)
...`;

    return await this.callDoubaoAPI(prompt);
  }

  // 生成内容
  async generateContent(domain) {
    const prompt = `请为"${domain}"领域生成5篇高质量的内容，要求：
1. 每篇内容独立完整
2. 适合社交媒体发布
3. 内容有价值、有趣味
4. 字数控制在200-500字
5. 每篇内容用"---"分隔

请直接输出内容，不要其他说明文字。`;

    const response = await this.callDoubaoAPI(prompt);
    
    // 解析生成的内容
    const contents = response
      .split('---')
      .map(content => content.trim())
      .filter(content => content.length > 50);
    
    return contents;
  }

  // 根据主题生成内容
  async generateContentByTopic(topic) {
    const prompt = `请围绕主题"${topic}"创作一篇小红书风格的文案，要求：
1. 标题吸引人，使用合适的emoji
2. 内容有价值、有趣味
3. 语言轻松活泼，贴近年轻人
4. 适当使用话题标签
5. 字数控制在200-400字
6. 结尾要有互动引导

请直接输出文案内容，不要其他说明文字。`;

    const response = await this.callDoubaoAPI(prompt);
    return [response]; // 返回数组格式保持一致
  }

  // 科普化内容
  async popularizeContent(content) {
    const prompt = `请将以下专业内容转换为通俗易懂的科普文案：

原始内容：
${content}

转换要求：
1. 使用通俗易懂的语言
2. 避免专业术语，如必须使用请加以解释
3. 增加生动的比喻和例子
4. 保持内容的准确性
5. 适合普通大众阅读理解

请直接输出科普化后的内容，不要其他说明文字。`;

    return await this.callDoubaoAPI(prompt);
  }

  // 提取文档摘要
  async extractDocumentSummary(content) {
    const prompt = `请分析以下文档内容，提取关键信息并生成结构化摘要：

文档内容：
${content.substring(0, 8000)} ${content.length > 8000 ? '...(内容已截断)' : ''}

请按以下JSON格式输出摘要：
{
  "title": "文档标题",
  "summary": "文档主要内容概述",
  "keyPoints": ["要点1", "要点2", "要点3"],
  "documentType": "文档类型",
  "wordCount": 大概字数
}

请确保输出是有效的JSON格式。`;

    const response = await this.callDoubaoAPI(prompt);
    
    try {
      // 尝试解析JSON
      const summary = JSON.parse(response);
      return summary;
    } catch (error) {
      console.error('Failed to parse summary JSON:', error);
      // 如果解析失败，返回基本格式
      return {
        title: "文档摘要",
        summary: response,
        keyPoints: [],
        documentType: "未知",
        wordCount: content.length
      };
    }
  }
}

exports.handler = async (event, context) => {
  // 处理CORS预检请求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // 验证请求方法
    if (event.httpMethod !== 'POST') {
      throw new Error('Only POST method is allowed');
    }

    // 解析请求体
    const body = JSON.parse(event.body);
    const { action, data } = body;

    console.log(`Processing request: ${action} using Doubao model`);

    const doubaoService = new DoubaoService();
    let result;

    switch (action) {
      case 'generateTopics':
        result = await doubaoService.generateTopics(data.domain);
        break;
      case 'optimize':
        result = await doubaoService.optimizeContent(data.text, data.platform, data.style);
        break;
      case 'generateVideoScript':
        result = await doubaoService.generateVideoScript(data.text, data.platform, data.style);
        break;
      case 'generateContent':
        result = await doubaoService.generateContent(data.domain);
        break;
      case 'generateContentByTopic':
        result = await doubaoService.generateContentByTopic(data.topic);
        break;
      case 'generateContentByTopicStream':
        // 简化的流式处理，实际上是非流式的
        result = await doubaoService.generateContentByTopic(data.topic);
        break;
      case 'popularize':
        result = await doubaoService.popularizeContent(data.content);
        break;
      case 'extractDocumentSummary':
        result = await doubaoService.extractDocumentSummary(data.content);
        break;
      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      body: JSON.stringify({ result })
    };

  } catch (error) {
    console.error('Error in ai-service function:', error);
    return {
      statusCode: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: error.message })
    };
  }
};
