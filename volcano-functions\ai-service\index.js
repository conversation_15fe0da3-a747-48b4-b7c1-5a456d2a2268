const express = require('express');
const DoubaoService = require('./doubaoService');
const { cleanXiaohongshuContent } = require('./textProcessing');

const app = express();

// 中间件
app.use(express.json({ limit: '10mb' }));

// CORS配置
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'authorization, x-client-info, apikey, content-type');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  next();
});

// 主处理函数
app.post('/ai-service', async (req, res) => {
  try {
    console.log('收到请求:', req.body);
    
    const { action, data } = req.body;
    
    if (!action) {
      return res.status(400).json({ error: 'Missing action parameter' });
    }

    // 使用豆包服务
    const doubaoService = new DoubaoService();
    let result;

    switch (action) {
      case 'generateTopics':
        if (!data.domain) {
          return res.status(400).json({ error: 'Missing domain parameter' });
        }
        result = await doubaoService.generateTopics(data.domain);
        break;
        
      case 'optimize':
        if (!data.text) {
          return res.status(400).json({ error: 'Missing text parameter' });
        }
        result = await doubaoService.optimizeContent(data.text, data.platform, data.style);
        break;
        
      case 'generateVideoScript':
        if (!data.text) {
          return res.status(400).json({ error: 'Missing text parameter' });
        }
        result = await doubaoService.generateVideoScript(data.text, data.platform, data.style);
        break;
        
      case 'generateContent':
        if (!data.domain) {
          return res.status(400).json({ error: 'Missing domain parameter' });
        }
        result = await doubaoService.generateContentByDomain(data.domain);
        break;
        
      case 'generateContentByTopic':
        if (!data.topic) {
          return res.status(400).json({ error: 'Missing topic parameter' });
        }
        result = await doubaoService.generateContentByTopic(data.topic);
        break;
        
      case 'generateContentByTopicStream':
        if (!data.topic) {
          return res.status(400).json({ error: 'Missing topic parameter' });
        }
        const contents = await doubaoService.generateContentByTopic(data.topic);
        const rawContent = contents.length > 0 ? contents[0] : '';
        result = cleanXiaohongshuContent(rawContent);
        break;
        
      case 'popularize':
        if (!data.content) {
          return res.status(400).json({ error: 'Missing content parameter' });
        }
        result = await doubaoService.popularizeContent(data.content);
        break;
        
      case 'extractDocumentSummary':
        if (!data.content) {
          return res.status(400).json({ error: 'Missing content parameter' });
        }
        result = await doubaoService.extractDocumentSummary(data.content);
        break;
        
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    console.log('处理结果:', typeof result === 'string' ? result.substring(0, 100) + '...' : result);
    
    res.json({ result });

  } catch (error) {
    console.error('处理请求时出错:', error);
    res.status(500).json({ error: error.message || '内部服务器错误' });
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 火山引擎云函数入口
exports.handler = async (event, context) => {
  try {
    console.log('收到事件:', JSON.stringify(event, null, 2));
    
    // 解析HTTP请求
    const { httpMethod, body, headers, queryStringParameters } = event;
    
    if (httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        },
        body: ''
      };
    }
    
    if (httpMethod === 'GET' && event.path === '/health') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() })
      };
    }
    
    if (httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }
    
    // 解析请求体
    let requestBody;
    try {
      requestBody = JSON.parse(body || '{}');
    } catch (error) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({ error: 'Invalid JSON' })
      };
    }
    
    const { action, data } = requestBody;
    
    if (!action) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({ error: 'Missing action parameter' })
      };
    }

    // 使用豆包服务
    const DoubaoService = require('./doubaoService');
    const { cleanXiaohongshuContent } = require('./textProcessing');
    const doubaoService = new DoubaoService();
    let result;

    switch (action) {
      case 'generateTopics':
        if (!data.domain) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing domain parameter' })
          };
        }
        result = await doubaoService.generateTopics(data.domain);
        break;
        
      case 'optimize':
        if (!data.text) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing text parameter' })
          };
        }
        result = await doubaoService.optimizeContent(data.text, data.platform, data.style);
        break;
        
      case 'generateVideoScript':
        if (!data.text) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing text parameter' })
          };
        }
        result = await doubaoService.generateVideoScript(data.text, data.platform, data.style);
        break;
        
      case 'generateContent':
        if (!data.domain) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing domain parameter' })
          };
        }
        result = await doubaoService.generateContentByDomain(data.domain);
        break;
        
      case 'generateContentByTopic':
        if (!data.topic) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing topic parameter' })
          };
        }
        result = await doubaoService.generateContentByTopic(data.topic);
        break;
        
      case 'generateContentByTopicStream':
        if (!data.topic) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing topic parameter' })
          };
        }
        const contents = await doubaoService.generateContentByTopic(data.topic);
        const rawContent = contents.length > 0 ? contents[0] : '';
        result = cleanXiaohongshuContent(rawContent);
        break;
        
      case 'popularize':
        if (!data.content) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing content parameter' })
          };
        }
        result = await doubaoService.popularizeContent(data.content);
        break;
        
      case 'extractDocumentSummary':
        if (!data.content) {
          return {
            statusCode: 400,
            headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
            body: JSON.stringify({ error: 'Missing content parameter' })
          };
        }
        result = await doubaoService.extractDocumentSummary(data.content);
        break;
        
      default:
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' },
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }

    console.log('处理结果:', typeof result === 'string' ? result.substring(0, 100) + '...' : result);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({ result })
    };

  } catch (error) {
    console.error('处理请求时出错:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({ error: error.message || '内部服务器错误' })
    };
  }
};

// 本地测试
if (require.main === module) {
  const port = process.env.PORT || 3000;
  app.listen(port, () => {
    console.log(`AI服务本地运行在端口 ${port}`);
  });
}