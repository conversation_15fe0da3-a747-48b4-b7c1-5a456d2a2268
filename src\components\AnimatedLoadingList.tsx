
import { useEffect, useState } from 'react';
import { <PERSON>rk<PERSON>, Brain, Lightbulb, Mountain, Link, Languages } from 'lucide-react';

interface AnimatedLoadingListProps {
  title?: string;
  description?: string;
}

const loadingItems = [
  {
    text: "正在汲取知识的海绵",
    icon: Sparkles,
  },
  {
    text: "正在制造美好的意外",
    icon: Lightbulb,
  },
  {
    text: "正在攀登文明金字塔",
    icon: Mountain,
  },
  {
    text: "正在链接神圣的主脑",
    icon: Link,
  },
  {
    text: "正在重新组织汉语言",
    icon: Languages,
  },
];

const AnimatedLoadingList = ({ title = "AI正在生成内容", description = "请稍候..." }: AnimatedLoadingListProps) => {
  const [currentItemIndex, setCurrentItemIndex] = useState(0);

  useEffect(() => {
    // 重置状态
    setCurrentItemIndex(0);

    // 逐行显示动画，每次只显示当前行
    const interval = setInterval(() => {
      setCurrentItemIndex(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= loadingItems.length) {
          // 所有项目都显示完后，重新开始
          return 0;
        }
        return nextIndex;
      });
    }, 1000); // 每1秒切换到下一行

    return () => {
      clearInterval(interval);
    };
  }, []);

  const currentItem = loadingItems[currentItemIndex];
  const Icon = currentItem.icon;

  return (
    <div className="text-center py-20">
      <div className="bg-white/90 backdrop-blur-md rounded-3xl p-8 shadow-xl max-w-2xl mx-auto border border-white/50">
        {/* 标题区域 */}
        <div className="mb-8">
          <Brain className="w-16 h-16 text-blue-600 mx-auto mb-4 animate-pulse" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600">{description}</p>
        </div>

        {/* 单行文字显示区域 - 固定高度避免跳动 */}
        <div className="h-20 flex items-center justify-center">
          <div
            key={currentItemIndex}
            className="flex items-center justify-center space-x-3 p-4 rounded-2xl 
              animate-fade-in
              hover:bg-blue-50/60 hover:text-blue-700 hover:scale-105
              bg-gray-50/30 border border-gray-200/30 transition-all duration-700 ease-out cursor-default"
          >
            <Icon className="w-5 h-5 text-blue-500 transition-all duration-300 hover:text-blue-700 hover:rotate-12" />
            <span className="text-gray-700 font-medium transition-colors duration-300 hover:text-blue-800 whitespace-nowrap">
              {currentItem.text}
            </span>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
            </div>
          </div>
        </div>

        {/* 进度指示器 */}
        <div className="mt-8">
          <div className="w-full bg-gray-200/50 rounded-full h-2 overflow-hidden">
            <div 
              className="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out animate-pulse"
              style={{ 
                width: `${((currentItemIndex + 1) / loadingItems.length) * 100}%`,
              }}
            ></div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            {Math.round(((currentItemIndex + 1) / loadingItems.length) * 100)}% 完成
          </p>
        </div>
      </div>
    </div>
  );
};

export default AnimatedLoadingList;
