
import { supabase } from "@/integrations/supabase/client";

export const saveGenerationHistory = async (
  userId: string,
  domain: string,
  topic: string | null,
  generatedContent: string
) => {
  try {
    console.log('保存生成历史记录:', { 
      userId, 
      domain, 
      topic, 
      contentLength: generatedContent.length,
      contentPreview: generatedContent.substring(0, 100) + '...' 
    });
    
    const { data, error } = await supabase
      .from('content_generation_history')
      .insert({
        user_id: userId,
        domain: domain,
        topic: topic,
        generated_content: generatedContent,
      })
      .select();

    if (error) {
      console.error('保存生成历史记录失败:', error);
      throw error;
    }
    
    console.log('生成历史记录保存成功:', data);
    return data;
  } catch (error) {
    console.error('保存生成历史记录失败:', error);
    throw error;
  }
};

export const saveOptimizationHistory = async (
  userId: string,
  originalText: string,
  optimizedText: string,
  platform: string,
  style: string,
  contentType: 'copywriting' | 'video-script' = 'copywriting'
) => {
  try {
    console.log('保存优化历史记录:', { 
      userId, 
      originalText: originalText.substring(0, 50) + '...', 
      platform, 
      style,
      contentType,
      optimizedLength: optimizedText.length
    });
    
    const { data, error } = await supabase
      .from('content_optimization_history')
      .insert({
        user_id: userId,
        original_text: originalText,
        optimized_text: optimizedText,
        platform: platform,
        style: style,
        content_type: contentType,
      })
      .select();

    if (error) {
      console.error('保存优化历史记录失败:', error);
      throw error;
    }
    
    console.log('优化历史记录保存成功:', data);
    return data;
  } catch (error) {
    console.error('保存优化历史记录失败:', error);
    throw error;
  }
};
