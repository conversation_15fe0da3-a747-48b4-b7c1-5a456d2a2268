
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, FileText, Loader2, X } from 'lucide-react';

interface FileUploadCardProps {
  file: File | null;
  isProcessing: boolean;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onConvert: () => void;
  onRemoveFile?: () => void;
  buttonText?: string;
  disabled?: boolean;
}

export const FileUploadCard: React.FC<FileUploadCardProps> = ({
  file,
  isProcessing,
  onFileChange,
  onConvert,
  onRemoveFile,
  buttonText,
  disabled = false
}) => {
  const defaultButtonText = isProcessing ? "正在转换文档..." : "开始转换";
  const displayButtonText = buttonText || defaultButtonText;

  return (
    <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0 shadow-blue-100/50">
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-2 rounded-lg">
            <Upload className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl">文档上传</span>
        </CardTitle>
        <CardDescription className="text-gray-600">
          支持PDF、Word文档格式，文件大小不超过500MB
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors rounded-xl p-8 text-center bg-gradient-to-br from-gray-50 to-white">
          <Input
            type="file"
            accept=".pdf,.doc,.docx"
            onChange={onFileChange}
            className="hidden"
            id="file-upload"
            disabled={disabled}
          />
          <label
            htmlFor="file-upload"
            className={`cursor-pointer flex flex-col items-center space-y-4 ${disabled ? 'pointer-events-none opacity-50' : ''}`}
          >
            <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center">
              <FileText className="w-8 h-8 text-blue-500" />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-900 mb-1">
                点击选择文件或拖拽到此处
              </p>
              <p className="text-sm text-gray-500">
                支持 PDF、DOC、DOCX 格式
              </p>
            </div>
          </label>
        </div>

        {file && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-4 rounded-xl">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-blue-900">{file.name}</p>
                <p className="text-sm text-blue-600">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              {onRemoveFile && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRemoveFile}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  disabled={disabled}
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        )}

        <Button
          onClick={onConvert}
          disabled={!file || isProcessing || disabled}
          className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-lg shadow-blue-200/50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-5 h-5 mr-3 animate-spin" />
              {displayButtonText}
            </>
          ) : (
            <>
              <Upload className="w-5 h-5 mr-3" />
              {displayButtonText}
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
