
export type DocumentStep = 'upload' | 'summary' | 'result';

export interface DisputePoint {
  序号: number;
  争议类型: string;
  争议描述: string;
  裁判要点: string;
}

export interface DocumentSummary {
  structured_disputes?: DisputePoint[];
  raw_summary?: string;
  error?: string;
  suggestion?: string;
  // 保持向后兼容的旧字段
  案件基本情况?: string;
  核心争议焦点?: string;
  法院认定与判决?: string;
}

export interface DocumentConvertState {
  file: File | null;
  isProcessing: boolean;
  isExtractingSummary: boolean;
  isGeneratingPopularized: boolean;
  originalContent: string;
  documentSummary: DocumentSummary | null;
  selectedSections: string[];
  popularizedContent: string;
  currentStep: DocumentStep;
}
