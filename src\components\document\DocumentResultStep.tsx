
import React from 'react';
import { FileUploadCard } from './FileUploadCard';
import { ContentDisplayCard } from './ContentDisplayCard';
import { Button } from '@/components/ui/button';
import { RotateCcw } from 'lucide-react';

interface DocumentResultStepProps {
  file: File | null;
  selectedFocusIds: string[];
  popularizedContent: string;
  originalContent: string;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onRestart: () => void;
}

export const DocumentResultStep: React.FC<DocumentResultStepProps> = ({
  file,
  selectedFocusIds,
  popularizedContent,
  originalContent,
  onFileChange,
  onRestart
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div className="space-y-6">
        <FileUploadCard
          file={file}
          isProcessing={false}
          onFileChange={onFileChange}
          onConvert={() => {}}
          onRemoveFile={onRestart}
          buttonText="上传新文档"
          disabled={false}
        />
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-green-800 mb-3">转换完成</h3>
          <p className="text-sm text-green-700 mb-4">
            已根据您选择的 {selectedFocusIds.length} 个内容部分生成科普文案，
            结果已自动保存到历史记录中。
          </p>
          <Button
            onClick={onRestart}
            variant="outline"
            className="w-full flex items-center justify-center space-x-2 bg-white hover:bg-gray-50"
          >
            <RotateCcw className="w-4 h-4" />
            <span>开始新的转换</span>
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        <ContentDisplayCard
          title="科普文案"
          content={popularizedContent}
          filename="科普文案.md"
        />

        <ContentDisplayCard
          title="原始内容"
          content={originalContent}
          filename="原始内容.md"
        />
      </div>
    </div>
  );
};
