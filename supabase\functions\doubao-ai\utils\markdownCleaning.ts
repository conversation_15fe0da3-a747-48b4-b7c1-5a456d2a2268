// Markdown format cleaning utilities
export function removeMarkdownFormatting(text: string): string {
  return text
    // Remove markdown bold and italic symbols, keep content
    .replace(/\*{1,3}([^*]*)\*{1,3}/g, '$1')
    .replace(/_{1,3}([^_]*)_{1,3}/g, '$1')
    // Remove markdown headers
    .replace(/#{1,6}\s*/g, '')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`{1,3}([^`]*)`{1,3}/g, '$1')
    // Remove links, keep link text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove quotes
    .replace(/^\s*>\s*/gm, '')
    // Remove list markers
    .replace(/^\s*[-*+]\s*/gm, '')
    .replace(/^\s*\d+\.\s*/gm, '')
    // Remove separators
    .replace(/^\s*[-=]{3,}\s*$/gm, '');
}

export function removeMarkdownButKeepHashtags(text: string): string {
  return text
    // Remove markdown bold and italic symbols, keep content
    .replace(/\*{1,3}([^*]*)\*{1,3}/g, '$1')
    .replace(/_{1,3}([^_]*)_{1,3}/g, '$1')
    // Remove markdown headers (line-start #) but keep hashtags
    .replace(/^#{1,6}\s+/gm, '')
    // Remove code blocks
    .replace(/`{1,3}([^`]*)`{1,3}/g, '$1')
    // Remove links, keep link text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove list markers
    .replace(/^\s*[-*+]\s*/gm, '')
    .replace(/^\s*\d+\.\s*/gm, '')
    .replace(/^\s*>\s*/gm, '');
}

export function cleanMarkdownText(text: string): string {
  return text
    // Remove markdown headers
    .replace(/#{1,6}\s*/g, '')
    // Remove bold and italic markers, keep content
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // Remove standalone asterisks and other format symbols
    .replace(/\*+/g, '')
    .replace(/#+/g, '')
    .replace(/`+/g, '')
    .replace(/~+/g, '')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`([^`]+)`/g, '$1')
    // Remove link markers, keep link text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove quote markers
    .replace(/^\s*>\s*/gm, '')
    // Remove list markers, keep content
    .replace(/^\s*[-*+]\s*/gm, '')
    .replace(/^\s*\d+\.\s*/gm, '')
    // Remove separators
    .replace(/^\s*[-=]{3,}\s*$/gm, '')
    // Remove other common format symbols
    .replace(/[【】\[\]]/g, '')
    .replace(/[《》<>]/g, '')
    // Keep paragraph structure - improved cleaning
    .replace(/\n{4,}/g, '\n\n\n')  // Max 3 line breaks
    .replace(/\n{2}/g, '||PARAGRAPH||')  // Temporary paragraph marker
    .replace(/\s{2,}/g, ' ')  // Clean inline extra spaces
    .replace(/\|\|PARAGRAPH\|\|/g, '\n\n')  // Restore paragraph separation
    // Remove leading/trailing whitespace
    .trim();
}
