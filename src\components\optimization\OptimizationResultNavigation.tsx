
import React from 'react';
import { Button } from '@/components/ui/button';
import { Copy, Refresh<PERSON>w, ChevronLeft, ChevronRight, Download } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface OptimizationResultNavigationProps {
  currentResultIndex: number;
  totalResults: number;
  isOptimizing: boolean;
  isLoadingHistory: boolean;
  hasContent: boolean;
  onPreviousResult: () => void;
  onNextResult: () => void;
  onRegenerate: () => void;
  onCopyCurrentResult: () => void;
  onDownloadImage?: () => void;
}

const OptimizationResultNavigation: React.FC<OptimizationResultNavigationProps> = ({
  currentResultIndex,
  totalResults,
  isOptimizing,
  isLoadingHistory,
  hasContent,
  onPreviousResult,
  onNextResult,
  onRegenerate,
  onCopyCurrentResult,
  onDownloadImage,
}) => {
  const isMobile = useIsMobile();

  if (isOptimizing || isLoadingHistory || !hasContent) {
    return null;
  }

  if (isMobile) {
    return (
      <div className="space-y-3">
        {/* 结果导航按钮 */}
        {totalResults > 1 && (
          <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
            <Button
              onClick={onPreviousResult}
              disabled={currentResultIndex === 0}
              size="sm"
              variant="outline"
              className="w-8 h-8 p-0 rounded-full border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <ChevronLeft className="w-3.5 h-3.5" />
            </Button>
            <span className="px-3 text-sm font-medium bg-gray-50/50 rounded-full py-1">
              {currentResultIndex + 1}/{totalResults}
            </span>
            <Button
              onClick={onNextResult}
              disabled={currentResultIndex === totalResults - 1}
              size="sm"
              variant="outline"
              className="w-8 h-8 p-0 rounded-full border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <ChevronRight className="w-3.5 h-3.5" />
            </Button>
          </div>
        )}
        
        {/* 操作按钮组 - 移动端使用网格布局 */}
        <div className="grid grid-cols-2 gap-2">
          {/* 重新生成按钮 */}
          <Button
            onClick={onRegenerate}
            size="sm"
            variant="outline"
            className="text-sm rounded-xl border-blue-200/70 hover:bg-blue-50/80 transition-all duration-200 flex items-center justify-center space-x-1 px-3 py-2 shadow-sm hover:shadow-md"
          >
            <RefreshCw className="w-3.5 h-3.5" />
            <span>重新生成</span>
          </Button>
          
          {/* 复制按钮 */}
          <Button
            onClick={onCopyCurrentResult}
            size="sm"
            variant="outline"
            className="text-sm rounded-xl border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 flex items-center justify-center space-x-1 px-3 py-2 shadow-sm hover:shadow-md"
          >
            <Copy className="w-3.5 h-3.5" />
            <span>复制</span>
          </Button>
        </div>
      </div>
    );
  }

  // PC端保持原有设计
  return (
    <div className="flex items-center space-x-3">
      {/* 结果导航按钮 */}
      {totalResults > 1 && (
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Button
            onClick={onPreviousResult}
            disabled={currentResultIndex === 0}
            size="sm"
            variant="outline"
            className="w-9 h-9 p-0 rounded-full border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="px-3 text-sm font-medium bg-gray-50/50 rounded-full py-1">
            {currentResultIndex + 1}/{totalResults}
          </span>
          <Button
            onClick={onNextResult}
            disabled={currentResultIndex === totalResults - 1}
            size="sm"
            variant="outline"
            className="w-9 h-9 p-0 rounded-full border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}
      
      {/* 操作按钮组 */}
      <div className="flex items-center space-x-2">
        {/* 重新生成按钮 */}
        <Button
          onClick={onRegenerate}
          size="sm"
          variant="outline"
          className="text-sm rounded-full border-blue-200/70 hover:bg-blue-50/80 transition-all duration-200 flex items-center space-x-2 px-4 py-2 shadow-sm hover:shadow-md"
        >
          <RefreshCw className="w-3.5 h-3.5" />
          <span>重新生成</span>
        </Button>
        
        {/* 下载按钮 */}
        {onDownloadImage && (
          <Button
            onClick={onDownloadImage}
            size="sm"
            variant="outline"
            className="text-sm rounded-full border-purple-200/70 hover:bg-purple-50/80 transition-all duration-200 flex items-center space-x-2 px-4 py-2 shadow-sm hover:shadow-md"
          >
            <Download className="w-3.5 h-3.5" />
            <span>下载图片</span>
          </Button>
        )}
        
        {/* 复制按钮 */}
        <Button
          onClick={onCopyCurrentResult}
          size="sm"
          variant="outline"
          className="text-sm rounded-full border-gray-200/70 hover:bg-gray-50/80 transition-all duration-200 flex items-center space-x-2 px-4 py-2 shadow-sm hover:shadow-md"
        >
          <Copy className="w-3.5 h-3.5" />
          <span>复制</span>
        </Button>
      </div>
    </div>
  );
};

export default OptimizationResultNavigation;
