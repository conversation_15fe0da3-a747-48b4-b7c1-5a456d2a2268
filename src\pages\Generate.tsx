import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { aiService } from "@/services/aiServiceFactory";
import { useAuth } from "@/contexts/AuthContext";
import { useGenerationHistory } from "@/hooks/useGenerationHistory";
import GenerateHeader from "@/components/GenerateHeader";
import GenerationLoading from "@/components/GenerationLoading";
import ContentCard from "@/components/ContentCard";
import UsageInstructions from "@/components/UsageInstructions";
import EmptyGeneration from "@/components/EmptyGeneration";
import { Button } from "@/components/ui/button";
import { Home, History } from "lucide-react";

const Generate = () => {
  const { domain } = useParams<{ domain: string }>();
  const [generatedContent, setGeneratedContent] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentDomainInfo, setCurrentDomainInfo] = useState<any>(null);
  const { user } = useAuth();
  const { saveHistory } = useGenerationHistory();
  const navigate = useNavigate();

  const domainMap: Record<string, any> = {
    marriage: { title: "婚姻家庭", tag: "婚姻财产", icon: "💝" },
    labor: { title: "劳动用工", tag: "劳动法律", icon: "💼" },
    debt: { title: "债务纠纷", tag: "债务追讨", icon: "💳" },
    property: { title: "房产纠纷", tag: "房产法律", icon: "🏠" },
    corporate: { title: "公司法务", tag: "公司法律", icon: "🏢" },
    consumer: { title: "消费维权", tag: "消费者权益", icon: "🛡️" },
    criminal: { title: "刑事辩护", tag: "刑事法律", icon: "⚖️" },
    contract: { title: "合同审查", tag: "合同法律", icon: "📄" }
  };

  useEffect(() => {
    if (domain && domainMap[domain] && user) {
      setCurrentDomainInfo(domainMap[domain]);
      generateContent();
    }
  }, [domain, user]);

  const generateContent = async () => {
    if (!domain || !user) {
      console.error('缺少必要参数:', { domain, user: user ? 'exists' : 'null' });
      return;
    }
    
    setIsGenerating(true);
    setGeneratedContent([]);
    
    try {
      console.log('开始生成内容:', { domain, userId: user.id, domainTitle: domainMap[domain]?.title });
      const contents = await aiService.generateContentByDomain(domain);
      console.log('生成内容完成:', contents.length + ' 条内容');
      setGeneratedContent(contents);
      
      // 保存每条生成的内容到历史记录
      console.log('开始保存历史记录到数据库...');
      const savePromises = contents.map(async (content, index) => {
        try {
          console.log(`保存第 ${index + 1} 条记录...`);
          await saveHistory(domain, domainMap[domain]?.title, content);
          console.log(`第 ${index + 1} 条历史记录保存成功`);
        } catch (error) {
          console.error(`保存第 ${index + 1} 条历史记录失败:`, error);
        }
      });
      
      // 等待所有保存操作完成
      const results = await Promise.allSettled(savePromises);
      console.log('所有历史记录保存操作完成:', results);
      
      // 检查是否有保存失败的记录
      const failed = results.filter(result => result.status === 'rejected');
      if (failed.length > 0) {
        console.error('部分历史记录保存失败:', failed);
      } else {
        console.log('所有历史记录保存成功!');
      }
      
      toast({
        title: "生成完成",
        description: `已为您生成${contents.length}条专业法律文案`,
      });
    } catch (error) {
      console.error('生成文案失败:', error);
      toast({
        title: "生成失败",
        description: error instanceof Error ? error.message : "生成过程中出现错误",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateContent = () => {
    generateContent();
    toast({
      title: "重新生成",
      description: "正在为您重新生成内容...",
    });
  };

  if (!currentDomainInfo || !user) {
    return <div>加载中...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <GenerateHeader 
        currentDomainInfo={currentDomainInfo}
        isGenerating={isGenerating}
        onRegenerate={regenerateContent}
      />

      <div className="max-w-6xl mx-auto px-6 py-8">
        {isGenerating ? (
          <GenerationLoading domainTitle={currentDomainInfo.title} />
        ) : generatedContent.length > 0 ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {generatedContent.map((content, index) => (
                <ContentCard 
                  key={index}
                  content={content}
                  index={index}
                  domainTag={currentDomainInfo.tag}
                />
              ))}
            </div>
            <UsageInstructions domainTitle={currentDomainInfo.title} />
            
            {/* 添加导航按钮 */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 pt-8">
              <Button
                onClick={() => navigate("/app")}
                variant="outline"
                size="lg"
                className="w-full sm:w-auto px-8 py-4 text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm border-gray-200 hover:border-blue-300 hover:bg-blue-50/50"
              >
                <Home className="w-5 h-5 mr-3" />
                返回首页
              </Button>
              <Button
                onClick={() => {
                  navigate("/app");
                  // 延迟一下再触发历史记录显示，确保页面已经加载
                  setTimeout(() => {
                    const event = new CustomEvent('showHistory');
                    window.dispatchEvent(event);
                  }, 100);
                }}
                variant="outline"
                size="lg"
                className="w-full sm:w-auto px-8 py-4 text-lg rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm border-gray-200 hover:border-purple-300 hover:bg-purple-50/50"
              >
                <History className="w-5 h-5 mr-3" />
                查看历史记录
              </Button>
            </div>
          </div>
        ) : (
          <EmptyGeneration onGenerate={generateContent} />
        )}
      </div>
    </div>
  );
};

export default Generate;
