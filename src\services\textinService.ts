
import { supabase } from "@/integrations/supabase/client";

export class TextinService {
  private async callEdgeFunction(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    const { data, error } = await supabase.functions.invoke('textin-convert', {
      body: formData
    });

    if (error) {
      console.error('Textin API调用失败:', error);
      throw new Error(error.message || "文档解析失败");
    }

    if (data.error) {
      throw new Error(data.error);
    }

    return data.markdown;
  }

  async convertToMarkdown(file: File): Promise<string> {
    try {
      const markdown = await this.callEdgeFunction(file);
      return markdown;
    } catch (error) {
      console.error('文档转换失败:', error);
      throw error;
    }
  }
}

export const textinService = new TextinService();
