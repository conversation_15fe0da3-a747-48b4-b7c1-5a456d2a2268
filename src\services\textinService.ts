
import { supabase } from "@/integrations/supabase/client";
import { appConfig } from "@/config/environment";
import { volcanoService } from "./volcanoService";

export class TextinService {
  private async callSupabaseEdgeFunction(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    const { data, error } = await supabase.functions.invoke('textin-convert', {
      body: formData
    });

    if (error) {
      console.error('Supabase Edge Function调用失败:', error);
      throw new Error(error.message || "文档解析失败");
    }

    if (data.error) {
      throw new Error(data.error);
    }

    return data.markdown;
  }

  async convertToMarkdown(file: File): Promise<string> {
    try {
      console.log('使用API提供商:', appConfig.apiProvider);

      if (appConfig.apiProvider === 'volcano') {
        // 使用火山引擎API
        console.log('使用火山引擎进行文档转换');
        return await volcanoService.convertToMarkdown(file);
      } else {
        // 使用Supabase Edge Functions
        console.log('使用Supabase Edge Functions进行文档转换');
        return await this.callSupabaseEdgeFunction(file);
      }
    } catch (error) {
      console.error('文档转换失败:', error);

      // 如果当前使用Supabase失败，尝试切换到火山引擎作为备用
      if (appConfig.apiProvider === 'supabase') {
        console.log('Supabase失败，尝试使用火山引擎作为备用...');
        try {
          return await volcanoService.convertToMarkdown(file);
        } catch (volcanoError) {
          console.error('火山引擎备用方案也失败:', volcanoError);
          throw error; // 抛出原始错误
        }
      }

      throw error;
    }
  }
}

export const textinService = new TextinService();
