
import { supabase } from "@/integrations/supabase/client";

export const saveDocumentConversion = async (
  originalFilename: string,
  originalContent: string,
  popularizedContent: string
) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('用户未登录');
    }

    console.log('保存文档转换历史记录:', { 
      originalFilename, 
      originalContentLength: originalContent.length,
      popularizedContentLength: popularizedContent.length 
    });
    
    const { data, error } = await supabase
      .from('document_conversion_history')
      .insert({
        user_id: user.id,
        original_filename: originalFilename,
        original_content: originalContent,
        popularized_content: popularizedContent,
      })
      .select();

    if (error) {
      console.error('保存文档转换历史记录失败:', error);
      throw error;
    }
    
    console.log('文档转换历史记录保存成功:', data);
    return data;
  } catch (error) {
    console.error('保存文档转换历史记录失败:', error);
    throw error;
  }
};
