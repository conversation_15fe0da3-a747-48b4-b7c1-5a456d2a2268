import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Scale, Sparkles, FileText, History, Heart, Briefcase, CreditCard, Home, Building, Shield, Target, Zap, Brain, TrendingUp, CheckCircle, Users, Clock } from 'lucide-react';
import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";

const Landing = () => {
  const navigate = useNavigate();
  const [titleNumber, setTitleNumber] = useState(0);
  
  const titles = useMemo(
    () => ["法律文案助手", "智能营销工具", "专业内容生成", "合规文案助手", "AI写作专家"],
    []
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === titles.length - 1) {
        setTitleNumber(0);
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2000);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, titles]);

  const features = [{
    icon: Sparkles,
    title: "智能内容优化",
    description: "将您的法律文案转化为更具吸引力、专业性的朋友圈内容",
    color: "bg-gradient-to-br from-blue-500 to-blue-700"
  }, {
    icon: FileText,
    title: "专业内容生成",
    description: "基于8个法律领域，智能生成符合营销规范的专业文案",
    color: "bg-gradient-to-br from-purple-500 to-purple-700"
  }, {
    icon: History,
    title: "历史记录管理",
    description: "自动保存所有优化和生成的内容，方便随时查看和复用",
    color: "bg-gradient-to-br from-green-500 to-green-700"
  }];

  const domains = [{
    icon: Heart,
    title: "婚姻家庭",
    desc: "离婚纠纷、财产分割"
  }, {
    icon: Briefcase,
    title: "劳动用工",
    desc: "劳动合同、工伤赔偿"
  }, {
    icon: CreditCard,
    title: "债务纠纷",
    desc: "借贷纠纷、欠款追讨"
  }, {
    icon: Home,
    title: "房产纠纷",
    desc: "买卖合同、租赁纠纷"
  }, {
    icon: Building,
    title: "公司法务",
    desc: "股权纠纷、合规咨询"
  }, {
    icon: Shield,
    title: "消费维权",
    desc: "产品质量、服务纠纷"
  }, {
    icon: Scale,
    title: "刑事辩护",
    desc: "刑事案件、取保候审"
  }, {
    icon: FileText,
    title: "合同审查",
    desc: "合同起草、纠纷处理"
  }];

  const benefits = [{
    icon: Clock,
    title: "节省时间",
    desc: "AI自动生成，告别手工编写"
  }, {
    icon: Target,
    title: "精准定位",
    desc: "针对法律营销场景优化"
  }, {
    icon: Brain,
    title: "智能优化",
    desc: "专业术语转化为通俗易懂"
  }, {
    icon: TrendingUp,
    title: "提升转化",
    desc: "提高文案吸引力和传播效果"
  }];

  const stats = [{
    number: "8+",
    label: "专业领域"
  }, {
    number: "10",
    label: "文案/次"
  }, {
    number: "99%",
    label: "合规率"
  }, {
    number: "5分钟",
    label: "生成时间"
  }];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* 导航栏 - 去掉右侧的重复按钮 */}
      <nav className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-2 rounded-xl">
                <Scale className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">法律文案助手</h1>
            </div>
            <Button onClick={() => navigate('/app')} className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-xl">立即体验</Button>
          </div>
        </div>
      </nav>

      {/* 英雄区域 */}
      <section className="max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            <span>AI驱动的</span>
            <span className="relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1">
              &nbsp;
              {titles.map((title, index) => (
                <motion.span
                  key={index}
                  className="absolute font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
                  initial={{ opacity: 0, y: "-100" }}
                  transition={{ type: "spring", stiffness: 50 }}
                  animate={
                    titleNumber === index
                      ? {
                          y: 0,
                          opacity: 1,
                        }
                      : {
                          y: titleNumber > index ? -150 : 150,
                          opacity: 0,
                        }
                  }
                >
                  {title}
                </motion.span>
              ))}
            </span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">解放双手，便宜大碗的文案内容</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button onClick={() => navigate('/app')} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl text-lg font-medium shadow-lg transform hover:scale-105 transition-all">
              免费开始使用
            </Button>
            <Button variant="outline" onClick={() => navigate('/demo')} className="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl text-lg font-medium">
              查看功能演示
            </Button>
          </div>
        </div>

        {/* 数据统计 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto">
          {stats.map((stat, index) => <div key={index} className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
              <div className="text-gray-600 text-sm">{stat.label}</div>
            </div>)}
        </div>
      </section>

      {/* 核心功能 */}
      <section className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">核心功能</h3>
          <p className="text-lg text-gray-600">三大核心功能，满足法律营销的所有需求</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <Card key={index} className="border-0 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 bg-white/90 backdrop-blur-sm rounded-3xl overflow-hidden group">
                <CardContent className="p-10 text-center">
                  <div className={`w-20 h-20 ${feature.color} rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-10 h-10 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h4>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* 专业领域 */}
      <section className="bg-white/50 backdrop-blur-sm py-20">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">覆盖八大法律领域</h3>
            <p className="text-lg text-gray-600">专业细分，精准服务不同法律业务场景</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {domains.map((domain, index) => {
              const IconComponent = domain.icon;
              return (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/90 backdrop-blur-sm rounded-3xl overflow-hidden group hover:scale-105">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <h5 className="font-bold text-gray-900 mb-3 text-lg">{domain.title}</h5>
                    <p className="text-sm text-gray-600 leading-relaxed">{domain.desc}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* 核心优势 */}
      <section className="max-w-7xl mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">为什么选择我们</h3>
          <p className="text-lg text-gray-600">专业、高效、合规的法律营销解决方案</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
          const IconComponent = benefit.icon;
          return <div key={index} className="text-center">
                <div className="bg-gradient-to-br from-blue-100 to-blue-200 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-8 h-8 text-blue-600" />
                </div>
                <h5 className="text-lg font-bold text-gray-900 mb-2">{benefit.title}</h5>
                <p className="text-gray-600 text-sm">{benefit.desc}</p>
              </div>;
        })}
        </div>
      </section>

      {/* 使用流程 - 简化卡片布局 */}
      <section className="bg-gradient-to-br from-blue-50/80 to-indigo-50/60 py-20">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">简单三步，快速开始</h3>
            <p className="text-lg text-gray-600">无需复杂设置，即刻体验AI文案生成</p>
          </div>
          
          {/* 简化卡片容器 */}
          <div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-12">
            {[{
              title: "选择功能",
              desc: "选择内容优化或生成功能",
              icon: Target,
              color: "from-blue-500 to-cyan-500"
            }, {
              title: "输入需求", 
              desc: "描述您的文案需求或选择领域",
              icon: FileText,
              color: "from-purple-500 to-pink-500"
            }, {
              title: "获取结果", 
              desc: "AI自动生成专业文案内容",
              icon: Zap,
              color: "from-emerald-500 to-blue-500"
            }].map((item, index) => {
              const IconComponent = item.icon;
              return (
                <motion.div 
                  key={index}
                  className="relative"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.2, duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  {/* 简化卡片内容 */}
                  <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 rounded-3xl overflow-hidden group w-72">
                    <CardContent className="p-8 text-center relative">
                      {/* 背景装饰 */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${item.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`}></div>
                      
                      {/* 图标 */}
                      <div className="relative mb-6">
                        <IconComponent className={`w-12 h-12 mx-auto text-gray-600 group-hover:text-blue-600 transition-colors duration-300`} />
                      </div>
                      
                      {/* 内容 */}
                      <h5 className="relative text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">{item.title}</h5>
                      <p className="relative text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">{item.desc}</p>
                      
                      {/* 底部装饰 */}
                      <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${item.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left`}></div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA区域 */}
      <section className="max-w-7xl mx-auto px-6 py-20 text-center">
        <div className="bg-gradient-to-br from-blue-600 to-blue-800 rounded-3xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">准备开始您的法律营销之旅？</h3>
          <p className="text-xl mb-8 opacity-90">
            立即体验AI驱动的法律文案生成，提升您的营销效果
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button onClick={() => navigate('/app')} className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-2xl text-lg font-medium">
              免费注册使用
            </Button>
            <Button variant="outline" onClick={() => navigate('/demo')} className="border-2 border-white text-white hover:bg-white/10 px-8 py-4 rounded-2xl text-lg font-medium">
              查看演示
            </Button>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="bg-blue-600 p-2 rounded-xl">
                <Scale className="h-6 w-6 text-white" />
              </div>
              <h4 className="text-xl font-bold">法律文案助手</h4>
            </div>
            <p className="text-gray-400 mb-6">
              专为法律专业人士设计的AI文案生成工具
            </p>
            <div className="flex justify-center space-x-6 text-sm text-gray-400">
              <span>© 2024 法律文案助手</span>
              <span>|</span>
              <span>隐私政策</span>
              <span>|</span>
              <span>服务条款</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
